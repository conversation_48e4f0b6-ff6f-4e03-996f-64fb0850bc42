interface GifFrame {
    imageData: ImageData;
    delay: number;
}

// Función para parsear GIF usando gifuct-js
const parseGIF = async (buffer: ArrayBuffer): Promise<any> => {
    // Importación dinámica para evitar problemas de SSR
    const { parseGIF: parseGIFLib } = await import('gifuct-js');
    return parseGIFLib(buffer);
};

export const extractFramesFromGif = async (gifFile: File): Promise<GifFrame[]> => {
    console.log('Iniciando extracción de frames del GIF...');

    try {
        // Convertir File a ArrayBuffer
        const arrayBuffer = await gifFile.arrayBuffer();
        console.log('ArrayBuffer creado, tamaño:', arrayBuffer.byteLength);

        // Parsear el GIF usando gifuct-js
        const { parseGIF: parseGIFLib, decompressFrames } = await import('gifuct-js');
        const gif = parseGIFLib(arrayBuffer);

        console.log('GIF parseado:', gif);
        console.log('Frames encontrados:', gif.frames?.length || 0);

        if (!gif.frames || gif.frames.length === 0) {
            console.warn('No se encontraron frames, usando fallback');
            return extractFramesBasic(gifFile);
        }

        // Descomprimir frames
        const frames = decompressFrames(gif, true);
        console.log('Frames descomprimidos:', frames.length);

        if (frames.length === 0) {
            console.warn('No se pudieron descomprimir frames, usando fallback');
            return extractFramesBasic(gifFile);
        }

        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        if (!ctx) {
            throw new Error('No se pudo crear contexto de canvas');
        }

        // Configurar canvas con las dimensiones del GIF
        canvas.width = gif.lsd.width;
        canvas.height = gif.lsd.height;
        console.log('Canvas configurado:', canvas.width, 'x', canvas.height);

        const extractedFrames: GifFrame[] = [];

        // Procesar cada frame
        for (let i = 0; i < frames.length; i++) {
            const frame = frames[i];
            console.log(`Procesando frame ${i + 1}/${frames.length}`);

            try {
                // Limpiar canvas si es necesario
                if (frame.disposalType === 2) {
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                }

                // Crear ImageData del frame
                const imageData = new ImageData(
                    new Uint8ClampedArray(frame.patch),
                    frame.dims.width,
                    frame.dims.height
                );

                // Dibujar el frame en el canvas
                ctx.putImageData(imageData, frame.dims.left, frame.dims.top);

                // Obtener el frame completo
                const fullFrameData = ctx.getImageData(0, 0, canvas.width, canvas.height);

                // Calcular delay (en milisegundos)
                const delay = (frame.delay || 10) * 10; // delay está en centésimas de segundo

                extractedFrames.push({
                    imageData: fullFrameData,
                    delay: Math.max(delay, 50) // delay mínimo de 50ms
                });

            } catch (frameError) {
                console.warn(`Error procesando frame ${i}:`, frameError);
                // Continuar con el siguiente frame
            }
        }

        console.log(`Extracción completada: ${extractedFrames.length} frames`);

        if (extractedFrames.length === 0) {
            console.warn('No se pudieron extraer frames, usando fallback');
            return extractFramesBasic(gifFile);
        }

        return extractedFrames;

    } catch (error) {
        console.error('Error extrayendo frames del GIF:', error);

        // Fallback: usar método básico con Image
        console.log('Usando método fallback...');
        return extractFramesBasic(gifFile);
    }
};

// Método fallback básico
const extractFramesBasic = async (gifFile: File): Promise<GifFrame[]> => {
    console.log('Usando método fallback básico...');

    return new Promise((resolve, reject) => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        if (!ctx) {
            reject(new Error('No se pudo crear contexto de canvas'));
            return;
        }

        const img = new Image();
        img.onload = () => {
            console.log('Imagen cargada en fallback:', img.width, 'x', img.height);

            canvas.width = img.width;
            canvas.height = img.height;

            ctx.drawImage(img, 0, 0);
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

            // Para GIFs estáticos o cuando no podemos extraer frames múltiples
            // creamos al menos un frame válido
            const frame = { imageData, delay: 100 };
            console.log('Frame fallback creado');

            resolve([frame]);
        };

        img.onerror = (error) => {
            console.error('Error cargando imagen en fallback:', error);
            reject(new Error('Error cargando imagen'));
        };

        img.src = URL.createObjectURL(gifFile);
    });
};

// Método alternativo usando canvas y requestAnimationFrame
const extractFramesAlternative = async (gifFile: File): Promise<GifFrame[]> => {
    console.log('Intentando método alternativo de extracción...');

    return new Promise((resolve, reject) => {
        const video = document.createElement('video');
        video.muted = true;
        video.loop = true;
        video.autoplay = true;

        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        if (!ctx) {
            reject(new Error('No se pudo crear contexto de canvas'));
            return;
        }

        const frames: GifFrame[] = [];
        let frameCount = 0;
        let lastTime = 0;

        video.onloadedmetadata = () => {
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;

            const captureFrame = (currentTime: number) => {
                if (currentTime - lastTime >= 100) { // Capturar cada 100ms
                    ctx.drawImage(video, 0, 0);
                    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

                    frames.push({
                        imageData,
                        delay: 100
                    });

                    lastTime = currentTime;
                    frameCount++;

                    if (frameCount < 30) { // Máximo 30 frames
                        requestAnimationFrame(captureFrame);
                    } else {
                        console.log(`Método alternativo: ${frames.length} frames capturados`);
                        resolve(frames);
                    }
                } else {
                    requestAnimationFrame(captureFrame);
                }
            };

            video.currentTime = 0;
            requestAnimationFrame(captureFrame);
        };

        video.onerror = () => {
            console.warn('Método alternativo falló, usando fallback básico');
            extractFramesBasic(gifFile).then(resolve).catch(reject);
        };

        video.src = URL.createObjectURL(gifFile);
    });
};

export const createGifFromFrames = async (frames: ImageData[], delay: number): Promise<Blob> => {
    console.log(`Iniciando reconstrucción de GIF con ${frames.length} frames, delay: ${delay}ms`);

    if (frames.length === 0) {
        throw new Error('No se pueden procesar los frames');
    }

    // Si solo hay 1 frame, crear PNG
    if (frames.length === 1) {
        console.log('Solo 1 frame detectado, creando PNG estático');
        return createPngFromFrame(frames[0]);
    }

    try {
        // Importación dinámica de gif.js
        const GIF = await import('gif.js');
        console.log('gif.js importado exitosamente');

        // Crear instancia de GIF con configuración optimizada
        const gif = new (GIF.default || GIF)({
            workers: 2,
            quality: 10,
            width: frames[0].width,
            height: frames[0].height,
            workerScript: '/gif.worker.js',
            background: '#FFFFFF',
            transparent: 0x00FFFFFF
        });

        console.log(`GIF configurado: ${frames[0].width}x${frames[0].height}`);

        // Crear canvas temporal para convertir ImageData a canvas
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        if (!ctx) {
            throw new Error('No se pudo crear contexto de canvas');
        }

        canvas.width = frames[0].width;
        canvas.height = frames[0].height;

        // Agregar cada frame al GIF
        for (let i = 0; i < frames.length; i++) {
            console.log(`Agregando frame ${i + 1}/${frames.length} al GIF`);

            // Limpiar canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Dibujar frame
            ctx.putImageData(frames[i], 0, 0);

            // Agregar al GIF con delay específico
            gif.addFrame(canvas, {
                delay: Math.max(delay, 50), // Mínimo 50ms
                copy: true
            });
        }

        console.log('Todos los frames agregados, iniciando renderizado...');

        // Renderizar el GIF
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Timeout renderizando GIF'));
            }, 30000); // 30 segundos timeout

            gif.on('finished', (blob: Blob) => {
                clearTimeout(timeout);
                console.log(`GIF renderizado exitosamente: ${blob.size} bytes`);
                resolve(blob);
            });

            gif.on('error', (error: Error) => {
                clearTimeout(timeout);
                console.error('Error renderizando GIF:', error);
                reject(error);
            });

            gif.on('progress', (progress: number) => {
                console.log(`Progreso renderizado GIF: ${(progress * 100).toFixed(1)}%`);
            });

            gif.render();
        });

    } catch (error) {
        console.error('Error creando GIF con gif.js:', error);

        // Fallback: crear PNG del primer frame
        console.log('Usando fallback: creando PNG del primer frame');
        return createPngFromFrame(frames[0]);
    }
};

// Función fallback para crear PNG
const createPngFromFrame = async (frameData: ImageData): Promise<Blob> => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    if (!ctx) {
        throw new Error('No se pudo crear contexto de canvas');
    }

    canvas.width = frameData.width;
    canvas.height = frameData.height;
    ctx.putImageData(frameData, 0, 0);

    return new Promise((resolve, reject) => {
        canvas.toBlob((blob) => {
            if (blob) {
                resolve(blob);
            } else {
                reject(new Error('Error creando imagen'));
            }
        }, 'image/png');
    });
};
