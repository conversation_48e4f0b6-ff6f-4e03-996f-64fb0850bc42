interface GifFrame {
    imageData: ImageData;
    delay: number;
}

export const extractFramesFromGif = async (gifFile: File): Promise<GifFrame[]> => {
    return new Promise((resolve, reject) => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        if (!ctx) {
            reject(new Error('No se pudo crear contexto de canvas'));
            return;
        }

        const img = new Image();
        img.onload = () => {
            canvas.width = img.width;
            canvas.height = img.height;
            
            // Para esta implementación básica, extraemos el primer frame
            // En una implementación completa necesitarías una librería como gif.js
            ctx.drawImage(img, 0, 0);
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            
            resolve([{ imageData, delay: 100 }]); // 100ms por defecto
        };
        
        img.onerror = () => reject(new Error('Error cargando imagen'));
        img.src = URL.createObjectURL(gifFile);
    });
};

export const createGifFromFrames = async (frames: ImageData[], delay: number): Promise<Blob> => {
    // Implementación básica que crea un PNG estático
    // Para GIF real necesitarías gif.js o similar
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    if (!ctx || frames.length === 0) {
        throw new Error('No se pueden procesar los frames');
    }
    
    canvas.width = frames[0].width;
    canvas.height = frames[0].height;
    
    ctx.putImageData(frames[0], 0, 0);
    
    return new Promise((resolve, reject) => {
        canvas.toBlob((blob) => {
            if (blob) {
                resolve(blob);
            } else {
                reject(new Error('Error creando imagen'));
            }
        }, 'image/png');
    });
};
