interface GifFrame {
    imageData: ImageData;
    delay: number;
}

// Función para parsear GIF usando gifuct-js
const parseGIF = async (buffer: ArrayBuffer): Promise<any> => {
    // Importación dinámica para evitar problemas de SSR
    const { parseGIF: parseGIFLib } = await import('gifuct-js');
    return parseGIFLib(buffer);
};

export const extractFramesFromGif = async (gifFile: File): Promise<GifFrame[]> => {
    try {
        // Convertir File a ArrayBuffer
        const arrayBuffer = await gifFile.arrayBuffer();

        // Parsear el GIF
        const gif = await parseGIF(arrayBuffer);

        if (!gif.frames || gif.frames.length === 0) {
            throw new Error('No se encontraron frames en el GIF');
        }

        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        if (!ctx) {
            throw new Error('No se pudo crear contexto de canvas');
        }

        // Configurar canvas con las dimensiones del GIF
        canvas.width = gif.lsd.width;
        canvas.height = gif.lsd.height;

        const frames: GifFrame[] = [];

        // Procesar cada frame
        for (let i = 0; i < gif.frames.length; i++) {
            const frame = gif.frames[i];

            // Limpiar canvas si es necesario
            if (frame.disposalType === 2) {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
            }

            // Crear ImageData del frame
            const imageData = new ImageData(
                new Uint8ClampedArray(frame.patch),
                frame.dims.width,
                frame.dims.height
            );

            // Dibujar el frame en el canvas
            ctx.putImageData(imageData, frame.dims.left, frame.dims.top);

            // Obtener el frame completo
            const fullFrameData = ctx.getImageData(0, 0, canvas.width, canvas.height);

            // Calcular delay (en milisegundos)
            const delay = frame.delay * 10; // delay está en centésimas de segundo

            frames.push({
                imageData: fullFrameData,
                delay: delay || 100 // delay mínimo de 100ms
            });
        }

        return frames;

    } catch (error) {
        console.error('Error extrayendo frames del GIF:', error);

        // Fallback: usar método básico con Image
        return extractFramesBasic(gifFile);
    }
};

// Método fallback básico
const extractFramesBasic = async (gifFile: File): Promise<GifFrame[]> => {
    return new Promise((resolve, reject) => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        if (!ctx) {
            reject(new Error('No se pudo crear contexto de canvas'));
            return;
        }

        const img = new Image();
        img.onload = () => {
            canvas.width = img.width;
            canvas.height = img.height;

            ctx.drawImage(img, 0, 0);
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

            resolve([{ imageData, delay: 100 }]);
        };

        img.onerror = () => reject(new Error('Error cargando imagen'));
        img.src = URL.createObjectURL(gifFile);
    });
};

export const createGifFromFrames = async (frames: ImageData[], delay: number): Promise<Blob> => {
    if (frames.length === 0) {
        throw new Error('No se pueden procesar los frames');
    }

    try {
        // Importación dinámica de gif.js
        const GIF = await import('gif.js');

        // Crear instancia de GIF
        const gif = new (GIF.default || GIF)({
            workers: 2,
            quality: 10,
            width: frames[0].width,
            height: frames[0].height,
            workerScript: '/gif.worker.js' // Necesitaremos copiar este archivo
        });

        // Crear canvas temporal para convertir ImageData a canvas
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        if (!ctx) {
            throw new Error('No se pudo crear contexto de canvas');
        }

        canvas.width = frames[0].width;
        canvas.height = frames[0].height;

        // Agregar cada frame al GIF
        for (const frame of frames) {
            ctx.putImageData(frame, 0, 0);
            gif.addFrame(canvas, { delay: delay });
        }

        // Renderizar el GIF
        return new Promise((resolve, reject) => {
            gif.on('finished', (blob: Blob) => {
                resolve(blob);
            });

            gif.on('error', (error: Error) => {
                reject(error);
            });

            gif.render();
        });

    } catch (error) {
        console.error('Error creando GIF con gif.js:', error);

        // Fallback: crear PNG del primer frame
        return createPngFromFrame(frames[0]);
    }
};

// Función fallback para crear PNG
const createPngFromFrame = async (frameData: ImageData): Promise<Blob> => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    if (!ctx) {
        throw new Error('No se pudo crear contexto de canvas');
    }

    canvas.width = frameData.width;
    canvas.height = frameData.height;
    ctx.putImageData(frameData, 0, 0);

    return new Promise((resolve, reject) => {
        canvas.toBlob((blob) => {
            if (blob) {
                resolve(blob);
            } else {
                reject(new Error('Error creando imagen'));
            }
        }, 'image/png');
    });
};
