export interface GPUInfo {
    vendor: string;
    renderer: string;
    supportsWebGL: boolean;
    supportsWebGPU: boolean;
    maxTextureSize: number;
    maxComputeWorkgroupSize?: number;
    preferredMethod: 'webgpu' | 'webgl' | 'cpu';
}

export interface GPUCapabilities {
    hasGPU: boolean;
    isAMD: boolean;
    isNVIDIA: boolean;
    isIntel: boolean;
    info: GPUInfo | null;
}

/**
 * Detecta las capacidades de GPU disponibles en el navegador
 */
export const detectGPUCapabilities = async (): Promise<GPUCapabilities> => {
    const capabilities: GPUCapabilities = {
        hasGPU: false,
        isAMD: false,
        isNVIDIA: false,
        isIntel: false,
        info: null
    };

    try {
        // Intentar detectar WebGPU primero (más moderno)
        const webgpuInfo = await detectWebGPU();
        if (webgpuInfo) {
            capabilities.hasGPU = true;
            capabilities.info = webgpuInfo;
            capabilities.isAMD = isAMDGPU(webgpuInfo.renderer);
            capabilities.isNVIDIA = isNVIDIAGPU(webgpuInfo.renderer);
            capabilities.isIntel = isIntelGPU(webgpuInfo.renderer);
            return capabilities;
        }

        // Fallback a WebGL
        const webglInfo = await detectWebGL();
        if (webglInfo) {
            capabilities.hasGPU = true;
            capabilities.info = webglInfo;
            capabilities.isAMD = isAMDGPU(webglInfo.renderer);
            capabilities.isNVIDIA = isNVIDIAGPU(webglInfo.renderer);
            capabilities.isIntel = isIntelGPU(webglInfo.renderer);
        }

    } catch (error) {
        console.warn('Error detectando GPU:', error);
    }

    return capabilities;
};

/**
 * Detecta capacidades WebGPU
 */
const detectWebGPU = async (): Promise<GPUInfo | null> => {
    if (!('gpu' in navigator)) {
        return null;
    }

    try {
        const adapter = await navigator.gpu.requestAdapter();
        if (!adapter) {
            return null;
        }

        const device = await adapter.requestDevice();
        const limits = adapter.limits;

        return {
            vendor: adapter.info?.vendor || 'Unknown',
            renderer: adapter.info?.description || 'Unknown WebGPU Device',
            supportsWebGL: false,
            supportsWebGPU: true,
            maxTextureSize: limits.maxTextureDimension2D || 8192,
            maxComputeWorkgroupSize: limits.maxComputeWorkgroupSizeX || 256,
            preferredMethod: 'webgpu'
        };

    } catch (error) {
        console.warn('WebGPU no disponible:', error);
        return null;
    }
};

/**
 * Detecta capacidades WebGL
 */
const detectWebGL = async (): Promise<GPUInfo | null> => {
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl2') || canvas.getContext('webgl');
    
    if (!gl) {
        return null;
    }

    try {
        const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
        const vendor = debugInfo ? 
            gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL) : 
            gl.getParameter(gl.VENDOR);
        const renderer = debugInfo ? 
            gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL) : 
            gl.getParameter(gl.RENDERER);

        return {
            vendor: vendor || 'Unknown',
            renderer: renderer || 'Unknown WebGL Device',
            supportsWebGL: true,
            supportsWebGPU: false,
            maxTextureSize: gl.getParameter(gl.MAX_TEXTURE_SIZE) || 2048,
            preferredMethod: 'webgl'
        };

    } catch (error) {
        console.warn('Error obteniendo información WebGL:', error);
        return {
            vendor: 'Unknown',
            renderer: 'Unknown WebGL Device',
            supportsWebGL: true,
            supportsWebGPU: false,
            maxTextureSize: 2048,
            preferredMethod: 'webgl'
        };
    }
};

/**
 * Determina si es una GPU AMD
 */
const isAMDGPU = (renderer: string): boolean => {
    const amdKeywords = ['amd', 'radeon', 'rx ', 'vega', 'navi', 'rdna'];
    return amdKeywords.some(keyword => 
        renderer.toLowerCase().includes(keyword)
    );
};

/**
 * Determina si es una GPU NVIDIA
 */
const isNVIDIAGPU = (renderer: string): boolean => {
    const nvidiaKeywords = ['nvidia', 'geforce', 'gtx', 'rtx', 'quadro', 'tesla'];
    return nvidiaKeywords.some(keyword => 
        renderer.toLowerCase().includes(keyword)
    );
};

/**
 * Determina si es una GPU Intel
 */
const isIntelGPU = (renderer: string): boolean => {
    const intelKeywords = ['intel', 'iris', 'uhd', 'hd graphics'];
    return intelKeywords.some(keyword => 
        renderer.toLowerCase().includes(keyword)
    );
};

/**
 * Obtiene el mejor método de procesamiento basado en las capacidades
 */
export const getBestProcessingMethod = (capabilities: GPUCapabilities): 'webgpu' | 'webgl' | 'cpu' => {
    if (!capabilities.hasGPU || !capabilities.info) {
        return 'cpu';
    }

    // Preferir WebGPU para GPUs modernas
    if (capabilities.info.supportsWebGPU) {
        return 'webgpu';
    }

    // Fallback a WebGL
    if (capabilities.info.supportsWebGL) {
        return 'webgl';
    }

    return 'cpu';
};

/**
 * Formatea la información de GPU para mostrar al usuario
 */
export const formatGPUInfo = (capabilities: GPUCapabilities): string => {
    if (!capabilities.hasGPU || !capabilities.info) {
        return 'Procesamiento por CPU (sin GPU detectada)';
    }

    const gpuType = capabilities.isNVIDIA ? 'NVIDIA' : 
                   capabilities.isAMD ? 'AMD' : 
                   capabilities.isIntel ? 'Intel' : 'Desconocida';
    
    const method = capabilities.info.supportsWebGPU ? 'WebGPU' : 'WebGL';
    
    return `${gpuType} GPU - ${capabilities.info.renderer} (${method})`;
};
