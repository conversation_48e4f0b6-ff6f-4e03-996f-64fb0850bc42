import { Frame } from '../components/FrameGallery';

export interface ExportOptions {
    format: 'png' | 'jpg';
    quality: number;
    directory: string;
}

export class FrameEditorService {
    private projectName: string = '';
    private exportedFiles: Map<string, string> = new Map(); // frameId -> fileName
    private fileWatchers: Map<string, number> = new Map();
    private watchInterval: number | null = null;
    private onFrameUpdated?: (frameId: string, imageData: ImageData) => void;

    constructor() {
        this.setupProject();
    }

    private setupProject(): void {
        // Crear nombre de proyecto único
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
        this.projectName = `gif-editor-${timestamp}`;

        console.log(`Proyecto iniciado: ${this.projectName}`);
    }

    /**
     * Exporta un frame como archivo para edición externa
     */
    async exportFrame(frame: Frame, options: ExportOptions = {
        format: 'png',
        quality: 0.95,
        directory: ''
    }): Promise<string> {
        try {
            const imageData = frame.editedImageData || frame.originalImageData;
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            if (!ctx) {
                throw new Error('No se pudo crear contexto de canvas');
            }

            canvas.width = imageData.width;
            canvas.height = imageData.height;
            ctx.putImageData(imageData, 0, 0);

            // Convertir a blob
            const blob = await new Promise<Blob>((resolve, reject) => {
                canvas.toBlob(
                    (blob) => {
                        if (blob) resolve(blob);
                        else reject(new Error('Error creando blob'));
                    },
                    `image/${options.format}`,
                    options.quality
                );
            });

            // Crear nombre de archivo con prefijo del proyecto
            const fileName = `${this.projectName}_frame_${frame.id}.${options.format}`;

            // Descargar archivo
            await this.downloadFile(blob, fileName);

            // Registrar archivo exportado
            this.exportedFiles.set(frame.id, fileName);
            this.registerFileForWatching(frame.id, fileName);

            console.log(`Frame ${frame.id} exportado como ${fileName}`);
            return fileName;

        } catch (error) {
            console.error('Error exportando frame:', error);
            throw error;
        }
    }

    /**
     * Descarga un archivo al sistema
     */
    private async downloadFile(blob: Blob, fileName: string): Promise<void> {
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = fileName;
        link.style.display = 'none';
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        URL.revokeObjectURL(url);
    }

    /**
     * Registra un archivo para monitoreo de cambios
     */
    private registerFileForWatching(frameId: string, filePath: string): void {
        this.fileWatchers.set(frameId, Date.now());
        console.log(`Registrado para monitoreo: ${frameId} -> ${filePath}`);
    }

    /**
     * Inicia el monitoreo de archivos editados
     */
    startWatching(onFrameUpdated: (frameId: string, imageData: ImageData) => void): void {
        this.onFrameUpdated = onFrameUpdated;
        
        if (this.watchInterval) {
            clearInterval(this.watchInterval);
        }

        this.watchInterval = window.setInterval(() => {
            this.checkForUpdatedFiles();
        }, 2000); // Verificar cada 2 segundos

        console.log('Iniciado monitoreo de archivos');
    }

    /**
     * Detiene el monitoreo de archivos
     */
    stopWatching(): void {
        if (this.watchInterval) {
            clearInterval(this.watchInterval);
            this.watchInterval = null;
        }
        console.log('Detenido monitoreo de archivos');
    }

    /**
     * Verifica si hay archivos actualizados
     * Nota: En un entorno web real, esto requeriría una API del servidor
     * Por ahora, simularemos con File API cuando el usuario seleccione archivos
     */
    private checkForUpdatedFiles(): void {
        // En un entorno web, esto sería limitado
        // Necesitaríamos que el usuario seleccione manualmente los archivos editados
        // O usar una extensión del navegador / aplicación Electron
        console.log('Verificando archivos actualizados...');
    }

    /**
     * Permite al usuario seleccionar archivos editados manualmente
     */
    async importEditedFrames(): Promise<{ frameId: string; imageData: ImageData }[]> {
        return new Promise((resolve, reject) => {
            const input = document.createElement('input');
            input.type = 'file';
            input.multiple = true;
            input.accept = 'image/*';
            
            input.onchange = async (event) => {
                const files = (event.target as HTMLInputElement).files;
                if (!files) {
                    resolve([]);
                    return;
                }

                const results: { frameId: string; imageData: ImageData }[] = [];

                for (const file of Array.from(files)) {
                    try {
                        // Extraer frameId del nombre del archivo
                        const match = file.name.match(/frame_([^.]+)\./);
                        if (!match) continue;
                        
                        const frameId = match[1];
                        const imageData = await this.loadImageFromFile(file);
                        
                        results.push({ frameId, imageData });
                        console.log(`Frame editado importado: ${frameId}`);
                        
                    } catch (error) {
                        console.error(`Error importando ${file.name}:`, error);
                    }
                }

                resolve(results);
            };

            input.onerror = () => reject(new Error('Error seleccionando archivos'));
            input.click();
        });
    }

    /**
     * Carga una imagen desde un archivo y la convierte a ImageData
     */
    private async loadImageFromFile(file: File): Promise<ImageData> {
        return new Promise((resolve, reject) => {
            const img = new Image();
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            if (!ctx) {
                reject(new Error('No se pudo crear contexto de canvas'));
                return;
            }

            img.onload = () => {
                canvas.width = img.width;
                canvas.height = img.height;
                ctx.drawImage(img, 0, 0);
                
                const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                resolve(imageData);
            };

            img.onerror = () => reject(new Error('Error cargando imagen'));
            img.src = URL.createObjectURL(file);
        });
    }

    /**
     * Abre el directorio de descargas (intenta varios métodos)
     */
    async openDownloadsFolder(): Promise<void> {
        try {
            // Método 1: Intentar abrir con File System Access API (Chrome/Edge)
            if ('showDirectoryPicker' in window) {
                try {
                    // @ts-ignore - API experimental
                    await window.showDirectoryPicker();
                    return;
                } catch (error) {
                    console.log('Usuario canceló o API no disponible');
                }
            }

            // Método 2: Crear un enlace especial para abrir carpeta
            this.showDownloadInstructions();

        } catch (error) {
            console.error('Error abriendo carpeta:', error);
            this.showDownloadInstructions();
        }
    }

    /**
     * Muestra instrucciones detalladas para encontrar los archivos
     */
    private showDownloadInstructions(): void {
        const exportedCount = this.exportedFiles.size;
        const fileList = Array.from(this.exportedFiles.values()).join('\n• ');

        const message = `
🎬 ARCHIVOS EXPORTADOS (${exportedCount} frames)

📁 UBICACIÓN:
Los archivos están en tu carpeta de DESCARGAS

📋 ARCHIVOS EXPORTADOS:
• ${fileList}

🛠️ CÓMO EDITAR:
1. Abre tu carpeta de Descargas (Ctrl+J en Chrome)
2. Busca los archivos que empiecen con "${this.projectName}"
3. Haz doble clic en cada imagen para abrirla
4. Edita con tu programa favorito:
   • Photoshop
   • GIMP (gratuito)
   • Paint.NET (gratuito)
   • Photopea.com (online)
   • Remove.bg (automático)

💾 IMPORTANTE:
• Guarda con el MISMO NOMBRE
• Mantén el formato PNG
• No cambies las dimensiones

🔄 DESPUÉS DE EDITAR:
• Vuelve aquí y haz clic en "Importar Editados"
• Selecciona los archivos que modificaste
• ¡Listo para juntar el GIF!

💡 CONSEJO:
Presiona Ctrl+J en tu navegador para abrir la carpeta de descargas rápidamente.
        `;

        alert(message);
    }

    /**
     * Crea un enlace directo para abrir la carpeta de descargas
     */
    createDownloadsFolderShortcut(): HTMLAnchorElement {
        const link = document.createElement('a');

        // Diferentes rutas según el sistema operativo
        const userAgent = navigator.userAgent.toLowerCase();
        let folderPath = '';

        if (userAgent.includes('win')) {
            // Windows
            folderPath = 'file:///C:/Users/' + (process.env.USERNAME || 'Usuario') + '/Downloads';
        } else if (userAgent.includes('mac')) {
            // macOS
            folderPath = 'file:///Users/' + (process.env.USER || 'usuario') + '/Downloads';
        } else {
            // Linux
            folderPath = 'file:///home/' + (process.env.USER || 'usuario') + '/Downloads';
        }

        link.href = folderPath;
        link.textContent = 'Abrir Carpeta de Descargas';
        link.target = '_blank';
        link.className = 'text-blue-400 hover:text-blue-300 underline';

        return link;
    }

    /**
     * Limpia archivos temporales
     */
    cleanup(): void {
        this.stopWatching();
        this.fileWatchers.clear();
        console.log('Limpieza completada');
    }

    /**
     * Obtiene estadísticas del proyecto
     */
    getStats(): {
        totalFrames: number;
        editedFrames: number;
        workingDirectory: string;
        isWatching: boolean;
    } {
        return {
            totalFrames: this.fileWatchers.size,
            editedFrames: 0, // Se actualizaría con archivos realmente editados
            workingDirectory: this.exportDirectory,
            isWatching: this.watchInterval !== null
        };
    }
}

// Instancia singleton
export const frameEditorService = new FrameEditorService();
