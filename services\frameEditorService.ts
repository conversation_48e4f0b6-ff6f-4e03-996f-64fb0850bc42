import { Frame } from '../components/FrameGallery';

export interface ExportOptions {
    format: 'png' | 'jpg';
    quality: number;
    directory: string;
}

export class FrameEditorService {
    private exportDirectory: string = '';
    private fileWatchers: Map<string, number> = new Map();
    private watchInterval: number | null = null;
    private onFrameUpdated?: (frameId: string, imageData: ImageData) => void;

    constructor() {
        this.setupExportDirectory();
    }

    private async setupExportDirectory(): Promise<void> {
        // Crear directorio temporal para frames
        const timestamp = Date.now();
        this.exportDirectory = `gif-editor-${timestamp}`;
        
        console.log(`Directorio de trabajo: ${this.exportDirectory}`);
    }

    /**
     * Exporta un frame como archivo para edición externa
     */
    async exportFrame(frame: Frame, options: ExportOptions = {
        format: 'png',
        quality: 0.95,
        directory: this.exportDirectory
    }): Promise<string> {
        try {
            const imageData = frame.editedImageData || frame.originalImageData;
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            if (!ctx) {
                throw new Error('No se pudo crear contexto de canvas');
            }

            canvas.width = imageData.width;
            canvas.height = imageData.height;
            ctx.putImageData(imageData, 0, 0);

            // Convertir a blob
            const blob = await new Promise<Blob>((resolve, reject) => {
                canvas.toBlob(
                    (blob) => {
                        if (blob) resolve(blob);
                        else reject(new Error('Error creando blob'));
                    },
                    `image/${options.format}`,
                    options.quality
                );
            });

            // Crear nombre de archivo
            const fileName = `frame_${frame.id}.${options.format}`;
            const filePath = `${options.directory}/${fileName}`;

            // Descargar archivo
            await this.downloadFile(blob, fileName);

            // Registrar archivo para monitoreo
            this.registerFileForWatching(frame.id, filePath);

            console.log(`Frame ${frame.id} exportado como ${fileName}`);
            return filePath;

        } catch (error) {
            console.error('Error exportando frame:', error);
            throw error;
        }
    }

    /**
     * Descarga un archivo al sistema
     */
    private async downloadFile(blob: Blob, fileName: string): Promise<void> {
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = fileName;
        link.style.display = 'none';
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        URL.revokeObjectURL(url);
    }

    /**
     * Registra un archivo para monitoreo de cambios
     */
    private registerFileForWatching(frameId: string, filePath: string): void {
        this.fileWatchers.set(frameId, Date.now());
        console.log(`Registrado para monitoreo: ${frameId} -> ${filePath}`);
    }

    /**
     * Inicia el monitoreo de archivos editados
     */
    startWatching(onFrameUpdated: (frameId: string, imageData: ImageData) => void): void {
        this.onFrameUpdated = onFrameUpdated;
        
        if (this.watchInterval) {
            clearInterval(this.watchInterval);
        }

        this.watchInterval = window.setInterval(() => {
            this.checkForUpdatedFiles();
        }, 2000); // Verificar cada 2 segundos

        console.log('Iniciado monitoreo de archivos');
    }

    /**
     * Detiene el monitoreo de archivos
     */
    stopWatching(): void {
        if (this.watchInterval) {
            clearInterval(this.watchInterval);
            this.watchInterval = null;
        }
        console.log('Detenido monitoreo de archivos');
    }

    /**
     * Verifica si hay archivos actualizados
     * Nota: En un entorno web real, esto requeriría una API del servidor
     * Por ahora, simularemos con File API cuando el usuario seleccione archivos
     */
    private checkForUpdatedFiles(): void {
        // En un entorno web, esto sería limitado
        // Necesitaríamos que el usuario seleccione manualmente los archivos editados
        // O usar una extensión del navegador / aplicación Electron
        console.log('Verificando archivos actualizados...');
    }

    /**
     * Permite al usuario seleccionar archivos editados manualmente
     */
    async importEditedFrames(): Promise<{ frameId: string; imageData: ImageData }[]> {
        return new Promise((resolve, reject) => {
            const input = document.createElement('input');
            input.type = 'file';
            input.multiple = true;
            input.accept = 'image/*';
            
            input.onchange = async (event) => {
                const files = (event.target as HTMLInputElement).files;
                if (!files) {
                    resolve([]);
                    return;
                }

                const results: { frameId: string; imageData: ImageData }[] = [];

                for (const file of Array.from(files)) {
                    try {
                        // Extraer frameId del nombre del archivo
                        const match = file.name.match(/frame_([^.]+)\./);
                        if (!match) continue;
                        
                        const frameId = match[1];
                        const imageData = await this.loadImageFromFile(file);
                        
                        results.push({ frameId, imageData });
                        console.log(`Frame editado importado: ${frameId}`);
                        
                    } catch (error) {
                        console.error(`Error importando ${file.name}:`, error);
                    }
                }

                resolve(results);
            };

            input.onerror = () => reject(new Error('Error seleccionando archivos'));
            input.click();
        });
    }

    /**
     * Carga una imagen desde un archivo y la convierte a ImageData
     */
    private async loadImageFromFile(file: File): Promise<ImageData> {
        return new Promise((resolve, reject) => {
            const img = new Image();
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            if (!ctx) {
                reject(new Error('No se pudo crear contexto de canvas'));
                return;
            }

            img.onload = () => {
                canvas.width = img.width;
                canvas.height = img.height;
                ctx.drawImage(img, 0, 0);
                
                const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                resolve(imageData);
            };

            img.onerror = () => reject(new Error('Error cargando imagen'));
            img.src = URL.createObjectURL(file);
        });
    }

    /**
     * Abre el directorio de trabajo (si es posible)
     */
    async openWorkingDirectory(): Promise<void> {
        // En un navegador web, esto es limitado
        // Podríamos mostrar instrucciones al usuario
        const message = `
Directorio de trabajo: ${this.exportDirectory}

Para editar los frames:
1. Los archivos se han descargado a tu carpeta de Descargas
2. Abre cada archivo con tu editor favorito (Photoshop, GIMP, Paint.NET, etc.)
3. Edita y guarda el archivo con el mismo nombre
4. Usa el botón "Importar Editados" para cargar los cambios

Programas recomendados:
• Adobe Photoshop
• GIMP (gratuito)
• Paint.NET (gratuito)
• Photopea (online, gratuito)
• Remove.bg (online)
        `;

        alert(message);
    }

    /**
     * Limpia archivos temporales
     */
    cleanup(): void {
        this.stopWatching();
        this.fileWatchers.clear();
        console.log('Limpieza completada');
    }

    /**
     * Obtiene estadísticas del proyecto
     */
    getStats(): {
        totalFrames: number;
        editedFrames: number;
        workingDirectory: string;
        isWatching: boolean;
    } {
        return {
            totalFrames: this.fileWatchers.size,
            editedFrames: 0, // Se actualizaría con archivos realmente editados
            workingDirectory: this.exportDirectory,
            isWatching: this.watchInterval !== null
        };
    }
}

// Instancia singleton
export const frameEditorService = new FrameEditorService();
