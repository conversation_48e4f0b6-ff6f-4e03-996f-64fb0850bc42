import { Frame } from '../components/FrameGallery';

export interface ExportOptions {
    format: 'png' | 'jpg';
    quality: number;
    directory: string;
}

export class FrameEditorService {
    private projectName: string = '';
    private selectedDirectory: FileSystemDirectoryHandle | null = null;
    private exportedFiles: Map<string, string> = new Map(); // frameId -> fileName
    private fileWatchers: Map<string, number> = new Map();
    private watchInterval: number | null = null;
    private onFrameUpdated?: (frameId: string, imageData: ImageData) => void;

    constructor() {
        this.setupProject();
    }

    private setupProject(): void {
        // Crear nombre de proyecto único
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
        this.projectName = `gif-editor-${timestamp}`;

        console.log(`Proyecto iniciado: ${this.projectName}`);
    }

    /**
     * Permite al usuario seleccionar una carpeta donde guardar los frames
     */
    async selectOutputDirectory(): Promise<boolean> {
        try {
            // Verificar si el navegador soporta File System Access API
            if (!('showDirectoryPicker' in window)) {
                throw new Error('Tu navegador no soporta selección de carpetas. Usa Chrome/Edge actualizado.');
            }

            // @ts-ignore - API experimental pero ampliamente soportada
            this.selectedDirectory = await window.showDirectoryPicker({
                mode: 'readwrite'
            });

            console.log(`Carpeta seleccionada: ${this.selectedDirectory.name}`);
            return true;

        } catch (error) {
            if (error instanceof Error && error.name === 'AbortError') {
                console.log('Usuario canceló la selección de carpeta');
                return false;
            }

            console.error('Error seleccionando carpeta:', error);
            throw error;
        }
    }

    /**
     * Obtiene la carpeta seleccionada o solicita seleccionar una
     */
    async ensureDirectorySelected(): Promise<boolean> {
        if (!this.selectedDirectory) {
            return await this.selectOutputDirectory();
        }
        return true;
    }

    /**
     * Exporta un frame como archivo PNG en la carpeta seleccionada
     */
    async exportFrame(frame: Frame, options: ExportOptions = {
        format: 'png',
        quality: 0.95,
        directory: ''
    }): Promise<string> {
        try {
            // Asegurar que hay una carpeta seleccionada
            const hasDirectory = await this.ensureDirectorySelected();
            if (!hasDirectory) {
                throw new Error('No se seleccionó carpeta de destino');
            }

            const imageData = frame.editedImageData || frame.originalImageData;
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            if (!ctx) {
                throw new Error('No se pudo crear contexto de canvas');
            }

            canvas.width = imageData.width;
            canvas.height = imageData.height;
            ctx.putImageData(imageData, 0, 0);

            // Convertir a blob
            const blob = await new Promise<Blob>((resolve, reject) => {
                canvas.toBlob(
                    (blob) => {
                        if (blob) resolve(blob);
                        else reject(new Error('Error creando blob'));
                    },
                    'image/png', // Siempre PNG
                    1.0 // Máxima calidad para PNG
                );
            });

            // Crear nombre de archivo
            const fileName = `frame_${frame.id.padStart(3, '0')}.png`;

            // Guardar archivo en la carpeta seleccionada
            await this.saveFileToDirectory(blob, fileName);

            // Registrar archivo exportado
            this.exportedFiles.set(frame.id, fileName);
            this.registerFileForWatching(frame.id, fileName);

            console.log(`Frame ${frame.id} guardado como ${fileName}`);
            return fileName;

        } catch (error) {
            console.error('Error exportando frame:', error);
            throw error;
        }
    }

    /**
     * Guarda un archivo en la carpeta seleccionada usando File System Access API
     */
    private async saveFileToDirectory(blob: Blob, fileName: string): Promise<void> {
        if (!this.selectedDirectory) {
            throw new Error('No hay carpeta seleccionada');
        }

        try {
            // Crear archivo en la carpeta seleccionada
            const fileHandle = await this.selectedDirectory.getFileHandle(fileName, {
                create: true
            });

            // Crear stream de escritura
            const writable = await fileHandle.createWritable();

            // Escribir el blob
            await writable.write(blob);

            // Cerrar el stream
            await writable.close();

            console.log(`Archivo guardado: ${fileName}`);

        } catch (error) {
            console.error(`Error guardando archivo ${fileName}:`, error);
            throw error;
        }
    }

    /**
     * Exporta todos los frames de una vez a la carpeta seleccionada
     */
    async exportAllFrames(
        frames: Frame[],
        onProgress?: (current: number, total: number) => void
    ): Promise<string[]> {
        try {
            // Asegurar que hay una carpeta seleccionada
            const hasDirectory = await this.ensureDirectorySelected();
            if (!hasDirectory) {
                throw new Error('No se seleccionó carpeta de destino');
            }

            const exportedFiles: string[] = [];

            for (let i = 0; i < frames.length; i++) {
                const frame = frames[i];

                try {
                    const fileName = await this.exportFrame(frame);
                    exportedFiles.push(fileName);

                    // Reportar progreso
                    if (onProgress) {
                        onProgress(i + 1, frames.length);
                    }

                    console.log(`Exportado ${i + 1}/${frames.length}: ${fileName}`);

                } catch (error) {
                    console.error(`Error exportando frame ${i}:`, error);
                    // Continuar con el siguiente frame
                }
            }

            console.log(`Exportación completada: ${exportedFiles.length}/${frames.length} frames`);
            return exportedFiles;

        } catch (error) {
            console.error('Error en exportación masiva:', error);
            throw error;
        }
    }

    /**
     * Abre la carpeta seleccionada en el explorador de archivos
     */
    async openSelectedDirectory(): Promise<void> {
        if (!this.selectedDirectory) {
            throw new Error('No hay carpeta seleccionada');
        }

        try {
            // Intentar abrir la carpeta (funcionalidad experimental)
            if ('showDirectoryPicker' in window) {
                // Crear un archivo temporal para "forzar" la apertura de la carpeta
                const tempFileName = '_abrir_carpeta.txt';
                const tempContent = new Blob([
                    'Esta carpeta contiene los frames exportados del GIF.\n\n' +
                    'Puedes eliminar este archivo.\n\n' +
                    'Edita los archivos PNG y luego importa los cambios en la aplicación.'
                ], { type: 'text/plain' });

                await this.saveFileToDirectory(tempContent, tempFileName);

                // Mostrar mensaje al usuario
                alert(`Carpeta abierta: ${this.selectedDirectory.name}\n\nSe ha creado un archivo temporal "${tempFileName}" para abrir la carpeta.\nPuedes eliminarlo después de editar los frames.`);

            } else {
                throw new Error('Funcionalidad no disponible en este navegador');
            }

        } catch (error) {
            console.error('Error abriendo carpeta:', error);
            throw error;
        }
    }

    /**
     * Descarga un archivo al sistema (método fallback)
     */
    private async downloadFile(blob: Blob, fileName: string): Promise<void> {
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = fileName;
        link.style.display = 'none';
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        URL.revokeObjectURL(url);
    }

    /**
     * Registra un archivo para monitoreo de cambios
     */
    private registerFileForWatching(frameId: string, filePath: string): void {
        this.fileWatchers.set(frameId, Date.now());
        console.log(`Registrado para monitoreo: ${frameId} -> ${filePath}`);
    }

    /**
     * Inicia el monitoreo de archivos editados
     */
    startWatching(onFrameUpdated: (frameId: string, imageData: ImageData) => void): void {
        this.onFrameUpdated = onFrameUpdated;
        
        if (this.watchInterval) {
            clearInterval(this.watchInterval);
        }

        this.watchInterval = window.setInterval(() => {
            this.checkForUpdatedFiles();
        }, 2000); // Verificar cada 2 segundos

        console.log('Iniciado monitoreo de archivos');
    }

    /**
     * Detiene el monitoreo de archivos
     */
    stopWatching(): void {
        if (this.watchInterval) {
            clearInterval(this.watchInterval);
            this.watchInterval = null;
        }
        console.log('Detenido monitoreo de archivos');
    }

    /**
     * Verifica si hay archivos actualizados
     * Nota: En un entorno web real, esto requeriría una API del servidor
     * Por ahora, simularemos con File API cuando el usuario seleccione archivos
     */
    private checkForUpdatedFiles(): void {
        // En un entorno web, esto sería limitado
        // Necesitaríamos que el usuario seleccione manualmente los archivos editados
        // O usar una extensión del navegador / aplicación Electron
        console.log('Verificando archivos actualizados...');
    }

    /**
     * Importa archivos editados desde la carpeta seleccionada
     */
    async importEditedFrames(): Promise<{ frameId: string; imageData: ImageData }[]> {
        try {
            if (!this.selectedDirectory) {
                // Si no hay carpeta seleccionada, usar método de selección manual
                return await this.importEditedFramesManual();
            }

            const results: { frameId: string; imageData: ImageData }[] = [];

            // Iterar sobre los archivos exportados conocidos
            for (const [frameId, fileName] of this.exportedFiles) {
                try {
                    // Intentar leer el archivo desde la carpeta
                    const fileHandle = await this.selectedDirectory.getFileHandle(fileName);
                    const file = await fileHandle.getFile();

                    // Verificar si el archivo fue modificado
                    const lastModified = file.lastModified;
                    const originalTime = this.fileWatchers.get(frameId) || 0;

                    if (lastModified > originalTime) {
                        const imageData = await this.loadImageFromFile(file);
                        results.push({ frameId, imageData });

                        // Actualizar tiempo de modificación
                        this.fileWatchers.set(frameId, lastModified);

                        console.log(`Frame editado importado: ${frameId} (${fileName})`);
                    }

                } catch (error) {
                    console.warn(`No se pudo leer ${fileName}:`, error);
                    // El archivo podría no existir o no ser accesible
                }
            }

            if (results.length === 0) {
                console.log('No se encontraron archivos editados, usando selección manual');
                return await this.importEditedFramesManual();
            }

            console.log(`Importados ${results.length} frames editados desde la carpeta`);
            return results;

        } catch (error) {
            console.error('Error importando desde carpeta:', error);
            // Fallback a selección manual
            return await this.importEditedFramesManual();
        }
    }

    /**
     * Método fallback para seleccionar archivos manualmente
     */
    private async importEditedFramesManual(): Promise<{ frameId: string; imageData: ImageData }[]> {
        return new Promise((resolve, reject) => {
            const input = document.createElement('input');
            input.type = 'file';
            input.multiple = true;
            input.accept = 'image/*';

            input.onchange = async (event) => {
                const files = (event.target as HTMLInputElement).files;
                if (!files) {
                    resolve([]);
                    return;
                }

                const results: { frameId: string; imageData: ImageData }[] = [];

                for (const file of Array.from(files)) {
                    try {
                        // Extraer frameId del nombre del archivo
                        const match = file.name.match(/frame_(\d+)\./);
                        if (!match) continue;

                        const frameId = match[1];
                        const imageData = await this.loadImageFromFile(file);

                        results.push({ frameId, imageData });
                        console.log(`Frame editado importado manualmente: ${frameId}`);

                    } catch (error) {
                        console.error(`Error importando ${file.name}:`, error);
                    }
                }

                resolve(results);
            };

            input.onerror = () => reject(new Error('Error seleccionando archivos'));
            input.click();
        });
    }

    /**
     * Carga una imagen desde un archivo y la convierte a ImageData
     */
    private async loadImageFromFile(file: File): Promise<ImageData> {
        return new Promise((resolve, reject) => {
            const img = new Image();
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            if (!ctx) {
                reject(new Error('No se pudo crear contexto de canvas'));
                return;
            }

            img.onload = () => {
                canvas.width = img.width;
                canvas.height = img.height;
                ctx.drawImage(img, 0, 0);
                
                const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                resolve(imageData);
            };

            img.onerror = () => reject(new Error('Error cargando imagen'));
            img.src = URL.createObjectURL(file);
        });
    }

    /**
     * Abre la carpeta de trabajo (carpeta seleccionada o descargas)
     */
    async openWorkingFolder(): Promise<void> {
        try {
            if (this.selectedDirectory) {
                await this.openSelectedDirectory();
            } else {
                // Si no hay carpeta seleccionada, mostrar instrucciones
                this.showDirectoryInstructions();
            }
        } catch (error) {
            console.error('Error abriendo carpeta:', error);
            this.showDirectoryInstructions();
        }
    }

    /**
     * Muestra instrucciones sobre la carpeta de trabajo
     */
    private showDirectoryInstructions(): void {
        const exportedCount = this.exportedFiles.size;
        const fileList = Array.from(this.exportedFiles.values()).join('\n• ');
        const directoryName = this.selectedDirectory?.name || 'No seleccionada';

        const message = `
🎬 ARCHIVOS EXPORTADOS (${exportedCount} frames)

📁 CARPETA DE TRABAJO:
${directoryName}

📋 ARCHIVOS EXPORTADOS:
• ${fileList}

🛠️ CÓMO EDITAR:
1. Los archivos están guardados en la carpeta que seleccionaste
2. Abre cada archivo PNG con tu programa favorito:
   • Photoshop
   • GIMP (gratuito)
   • Paint.NET (gratuito)
   • Photopea.com (online)
   • Remove.bg (automático)

💾 IMPORTANTE:
• Guarda con el MISMO NOMBRE (frame_000.png, frame_001.png, etc.)
• Mantén el formato PNG
• No cambies las dimensiones
• Preserva la transparencia

🔄 DESPUÉS DE EDITAR:
• Vuelve aquí y haz clic en "Importar Editados"
• Los cambios se detectarán automáticamente
• ¡Listo para juntar el GIF!

💡 CONSEJO:
Si no puedes encontrar la carpeta, haz clic en "Seleccionar Carpeta" para elegir una nueva ubicación.
        `;

        alert(message);
    }

    /**
     * Crea un enlace directo para abrir la carpeta de descargas
     */
    createDownloadsFolderShortcut(): HTMLAnchorElement {
        const link = document.createElement('a');

        // Diferentes rutas según el sistema operativo
        const userAgent = navigator.userAgent.toLowerCase();
        let folderPath = '';

        if (userAgent.includes('win')) {
            // Windows
            folderPath = 'file:///C:/Users/' + (process.env.USERNAME || 'Usuario') + '/Downloads';
        } else if (userAgent.includes('mac')) {
            // macOS
            folderPath = 'file:///Users/' + (process.env.USER || 'usuario') + '/Downloads';
        } else {
            // Linux
            folderPath = 'file:///home/' + (process.env.USER || 'usuario') + '/Downloads';
        }

        link.href = folderPath;
        link.textContent = 'Abrir Carpeta de Descargas';
        link.target = '_blank';
        link.className = 'text-blue-400 hover:text-blue-300 underline';

        return link;
    }

    /**
     * Limpia archivos temporales
     */
    cleanup(): void {
        this.stopWatching();
        this.fileWatchers.clear();
        console.log('Limpieza completada');
    }

    /**
     * Obtiene estadísticas del proyecto
     */
    getStats(): {
        totalFrames: number;
        exportedFrames: number;
        selectedDirectory: string;
        isWatching: boolean;
        hasDirectoryAccess: boolean;
    } {
        return {
            totalFrames: this.fileWatchers.size,
            exportedFrames: this.exportedFiles.size,
            selectedDirectory: this.selectedDirectory?.name || 'No seleccionada',
            isWatching: this.watchInterval !== null,
            hasDirectoryAccess: !!this.selectedDirectory
        };
    }

    /**
     * Obtiene información sobre la carpeta seleccionada
     */
    getDirectoryInfo(): {
        name: string;
        hasAccess: boolean;
        exportedFiles: string[];
    } {
        return {
            name: this.selectedDirectory?.name || 'No seleccionada',
            hasAccess: !!this.selectedDirectory,
            exportedFiles: Array.from(this.exportedFiles.values())
        };
    }
}

// Instancia singleton
export const frameEditorService = new FrameEditorService();
