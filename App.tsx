
import React, { useState, useCallback, useEffect } from 'react';
import { ImageUploader } from './components/ImageUploader';
import { LoadingSpinner } from './components/LoadingSpinner';
import { FrameGallery, Frame } from './components/FrameGallery';
import { WorkflowInstructions } from './components/WorkflowInstructions';
import { DownloadIcon, SparklesIcon, XCircleIcon, FolderIcon } from './components/Icons';
import { extractFramesFromGif, createGifFromFrames } from './utils/gifUtils';
import { frameEditorService } from './services/frameEditorService';

const App: React.FC = () => {
    const [originalFile, setOriginalFile] = useState<File | null>(null);
    const [originalGifUrl, setOriginalGifUrl] = useState<string | null>(null);
    const [frames, setFrames] = useState<Frame[]>([]);
    const [isExtracting, setIsExtracting] = useState<boolean>(false);
    const [isReconstructing, setIsReconstructing] = useState<boolean>(false);
    const [isWatching, setIsWatching] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const [finalGifUrl, setFinalGifUrl] = useState<string | null>(null);
    const [selectedDirectory, setSelectedDirectory] = useState<string>('No seleccionada');
    const [exportProgress, setExportProgress] = useState<{ current: number; total: number } | null>(null);

    // Limpiar al desmontar
    useEffect(() => {
        return () => {
            frameEditorService.cleanup();
        };
    }, []);

    const handleFileSelect = useCallback(async (file: File) => {
        setOriginalFile(file);
        setOriginalGifUrl(URL.createObjectURL(file));
        setFrames([]);
        setFinalGifUrl(null);
        setError(null);

        // Si es un GIF, extraer frames automáticamente
        if (file.type === 'image/gif') {
            await extractFrames(file);
        } else {
            // Para imágenes estáticas, crear un solo frame
            await createSingleFrame(file);
        }
    }, []);

    const extractFrames = useCallback(async (gifFile: File) => {
        setIsExtracting(true);
        setError(null);

        try {
            console.log('Extrayendo frames del GIF...');
            const gifFrames = await extractFramesFromGif(gifFile);

            const frames: Frame[] = gifFrames.map((frame, index) => ({
                id: `frame_${index.toString().padStart(3, '0')}`,
                originalImageData: frame.imageData,
                delay: frame.delay,
                isEdited: false
            }));

            setFrames(frames);
            console.log(`${frames.length} frames extraídos exitosamente`);

        } catch (error) {
            console.error('Error extrayendo frames:', error);
            setError(`Error extrayendo frames: ${error instanceof Error ? error.message : 'Error desconocido'}`);
        } finally {
            setIsExtracting(false);
        }
    }, []);

    const createSingleFrame = useCallback(async (imageFile: File) => {
        setIsExtracting(true);
        setError(null);

        try {
            const imageData = await loadImageToImageData(imageFile);

            const frame: Frame = {
                id: 'frame_000',
                originalImageData: imageData,
                delay: 1000, // 1 segundo para imagen estática
                isEdited: false
            };

            setFrames([frame]);
            console.log('Imagen estática cargada como frame único');

        } catch (error) {
            console.error('Error cargando imagen:', error);
            setError(`Error cargando imagen: ${error instanceof Error ? error.message : 'Error desconocido'}`);
        } finally {
            setIsExtracting(false);
        }
    }, []);

    const loadImageToImageData = useCallback(async (imageFile: File): Promise<ImageData> => {
        return new Promise((resolve, reject) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            if (!ctx) {
                reject(new Error('No se pudo crear contexto de canvas'));
                return;
            }

            const img = new Image();
            img.onload = () => {
                canvas.width = img.width;
                canvas.height = img.height;
                ctx.drawImage(img, 0, 0);
                const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                resolve(imageData);
            };

            img.onerror = () => reject(new Error('Error cargando imagen'));
            img.src = URL.createObjectURL(imageFile);
        });
    }, []);

    const handleExportFrame = useCallback(async (frame: Frame) => {
        try {
            await frameEditorService.exportFrame(frame);
            console.log(`Frame ${frame.id} exportado exitosamente`);

        } catch (error) {
            console.error('Error exportando frame:', error);
            setError(`Error exportando frame: ${error instanceof Error ? error.message : 'Error desconocido'}`);
        }
    }, []);

    const handleSelectDirectory = useCallback(async () => {
        try {
            const selected = await frameEditorService.selectOutputDirectory();
            if (selected) {
                const dirInfo = frameEditorService.getDirectoryInfo();
                setSelectedDirectory(dirInfo.name);
                console.log(`Carpeta seleccionada: ${dirInfo.name}`);
            }
        } catch (error) {
            console.error('Error seleccionando carpeta:', error);
            setError(`Error seleccionando carpeta: ${error instanceof Error ? error.message : 'Error desconocido'}`);
        }
    }, []);

    const handleOpenFolder = useCallback(async () => {
        try {
            await frameEditorService.openWorkingFolder();
        } catch (error) {
            console.error('Error abriendo carpeta:', error);
            setError(`Error abriendo carpeta: ${error instanceof Error ? error.message : 'Error desconocido'}`);
        }
    }, []);

    const handleExportAllFrames = useCallback(async () => {
        if (frames.length === 0) return;

        setIsExtracting(true);
        setError(null);
        setExportProgress({ current: 0, total: frames.length });

        try {
            console.log(`Exportando ${frames.length} frames...`);

            const exportedFiles = await frameEditorService.exportAllFrames(
                frames,
                (current, total) => {
                    setExportProgress({ current, total });
                }
            );

            console.log(`Exportación completada: ${exportedFiles.length} archivos`);

            // Actualizar información de directorio
            const dirInfo = frameEditorService.getDirectoryInfo();
            setSelectedDirectory(dirInfo.name);

        } catch (error) {
            console.error('Error exportando frames:', error);
            setError(`Error exportando frames: ${error instanceof Error ? error.message : 'Error desconocido'}`);
        } finally {
            setIsExtracting(false);
            setExportProgress(null);
        }
    }, [frames]);

    const handleFrameUpdate = useCallback((frameId: string, newImageData: ImageData) => {
        setFrames(prevFrames =>
            prevFrames.map(frame =>
                frame.id === frameId
                    ? { ...frame, editedImageData: newImageData, isEdited: true }
                    : frame
            )
        );
        console.log(`Frame ${frameId} actualizado`);
    }, []);

    const handleImportEditedFrames = useCallback(async () => {
        try {
            const editedFrames = await frameEditorService.importEditedFrames();

            editedFrames.forEach(({ frameId, imageData }) => {
                handleFrameUpdate(frameId, imageData);
            });

            if (editedFrames.length > 0) {
                console.log(`${editedFrames.length} frames editados importados`);
            }

        } catch (error) {
            console.error('Error importando frames editados:', error);
            setError(`Error importando frames: ${error instanceof Error ? error.message : 'Error desconocido'}`);
        }
    }, [handleFrameUpdate]);

    const handleReconstructGif = useCallback(async () => {
        if (frames.length === 0) {
            setError('No hay frames para reconstruir');
            return;
        }

        setIsReconstructing(true);
        setError(null);

        try {
            console.log('Reconstruyendo GIF...');

            // Usar frames editados si están disponibles, sino los originales
            const framesToUse = frames.map(frame => ({
                imageData: frame.editedImageData || frame.originalImageData,
                delay: frame.delay
            }));

            const reconstructedGif = await createGifFromFrames(
                framesToUse.map(f => f.imageData),
                framesToUse[0]?.delay || 100
            );

            const gifUrl = URL.createObjectURL(reconstructedGif);
            setFinalGifUrl(gifUrl);

            console.log(`GIF reconstruido exitosamente: ${reconstructedGif.size} bytes`);

        } catch (error) {
            console.error('Error reconstruyendo GIF:', error);
            setError(`Error reconstruyendo GIF: ${error instanceof Error ? error.message : 'Error desconocido'}`);
        } finally {
            setIsReconstructing(false);
        }
    }, [frames]);

    const handleStartWatching = useCallback(() => {
        frameEditorService.startWatching(handleFrameUpdate);
        setIsWatching(true);
        console.log('Monitoreo de archivos iniciado');
    }, [handleFrameUpdate]);

    const handleStopWatching = useCallback(() => {
        frameEditorService.stopWatching();
        setIsWatching(false);
        console.log('Monitoreo de archivos detenido');
    }, []);

    const handleReset = useCallback(() => {
        setOriginalFile(null);
        setOriginalGifUrl(null);
        setFrames([]);
        setFinalGifUrl(null);
        setError(null);
        frameEditorService.cleanup();
        setIsWatching(false);
    }, []);



    return (
        <div className="min-h-screen bg-gray-900 text-gray-100 flex flex-col items-center p-4 sm:p-6 lg:p-8">
            <header className="w-full max-w-7xl text-center mb-8">
                <h1 className="text-4xl sm:text-5xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-cyan-400 mb-2">
                    Editor de Frames GIF
                </h1>
                <p className="text-lg text-gray-400">
                    Separa frames, edita con programas externos y reconstruye tu GIF
                </p>
            </header>

            <main className="w-full max-w-7xl flex-grow">
                {frames.length === 0 ? (
                    <div className="w-full">
                        <WorkflowInstructions />
                        <ImageUploader onFileSelect={handleFileSelect} />

                        {isExtracting && (
                            <div className="mt-8 flex flex-col items-center">
                                <LoadingSpinner />
                                <p className="mt-4 text-gray-400">
                                    {exportProgress
                                        ? `Exportando frames: ${exportProgress.current}/${exportProgress.total}`
                                        : 'Extrayendo frames...'
                                    }
                                </p>
                                {exportProgress && (
                                    <div className="w-64 bg-gray-700 rounded-full h-2 mt-2">
                                        <div
                                            className="bg-gradient-to-r from-orange-500 to-red-500 h-2 rounded-full transition-all duration-300"
                                            style={{ width: `${(exportProgress.current / exportProgress.total) * 100}%` }}
                                        ></div>
                                    </div>
                                )}
                            </div>
                        )}
                    </div>
                ) : (
                    <div className="w-full space-y-6">
                        {/* Barra de herramientas */}
                        <div className="bg-gray-800 rounded-xl p-4 border border-gray-700">
                            <div className="flex flex-wrap items-center justify-between gap-4">
                                <div className="flex flex-col space-y-2">
                                    <div className="flex items-center space-x-4">
                                        <h2 className="text-xl font-bold text-gray-200">
                                            Proyecto: {originalFile?.name}
                                        </h2>
                                        <span className="text-sm text-gray-400">
                                            {frames.length} frames • {frames.filter(f => f.isEdited).length} editados
                                        </span>
                                    </div>
                                    <div className="flex items-center space-x-2 text-sm">
                                        <span className="text-gray-400">Carpeta:</span>
                                        <span className="text-emerald-400 font-medium">{selectedDirectory}</span>
                                        {selectedDirectory === 'No seleccionada' && (
                                            <span className="text-yellow-400">(Selecciona una carpeta para guardar)</span>
                                        )}
                                    </div>
                                </div>

                                <div className="flex items-center space-x-2">
                                    <button
                                        onClick={handleSelectDirectory}
                                        className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-medium transition-colors flex items-center space-x-2"
                                    >
                                        <span>📂</span>
                                        <span>Seleccionar Carpeta</span>
                                    </button>

                                    <button
                                        onClick={handleExportAllFrames}
                                        disabled={isExtracting}
                                        className="px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded-lg font-medium transition-colors flex items-center space-x-2 disabled:opacity-50"
                                    >
                                        <span>📤</span>
                                        <span>
                                            {isExtracting
                                                ? `Exportando ${exportProgress?.current || 0}/${exportProgress?.total || 0}...`
                                                : 'Exportar Todos'
                                            }
                                        </span>
                                    </button>

                                    <button
                                        onClick={handleOpenFolder}
                                        disabled={selectedDirectory === 'No seleccionada'}
                                        className="px-4 py-2 bg-emerald-600 hover:bg-emerald-700 text-white rounded-lg font-medium transition-colors flex items-center space-x-2 disabled:opacity-50 disabled:bg-gray-600"
                                    >
                                        <span>📁</span>
                                        <span>Abrir Carpeta</span>
                                    </button>

                                    <button
                                        onClick={handleImportEditedFrames}
                                        className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors flex items-center space-x-2"
                                    >
                                        <FolderIcon className="w-4 h-4" />
                                        <span>Importar Editados</span>
                                    </button>

                                    <button
                                        onClick={handleReconstructGif}
                                        disabled={isReconstructing}
                                        className="px-6 py-2 bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white rounded-lg font-bold transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                                    >
                                        <SparklesIcon className="w-5 h-5" />
                                        <span>{isReconstructing ? 'Juntando...' : 'Juntar GIF'}</span>
                                    </button>

                                    <button
                                        onClick={handleReset}
                                        className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded-lg font-medium transition-colors"
                                    >
                                        Nuevo Proyecto
                                    </button>
                                </div>
                            </div>
                        </div>

                        {/* Galería de frames */}
                        <FrameGallery
                            frames={frames}
                            onFrameUpdate={handleFrameUpdate}
                            onExportFrame={handleExportFrame}
                            onOpenFolder={handleOpenFolder}
                            isWatching={isWatching}
                        />

                        {/* Resultado final */}
                        {finalGifUrl && (
                            <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
                                <h3 className="text-xl font-bold text-gray-200 mb-4">GIF Final</h3>
                                <div className="flex flex-col md:flex-row items-center gap-6">
                                    <div className="flex-1">
                                        <img
                                            src={finalGifUrl}
                                            alt="GIF Final"
                                            className="max-w-full h-auto rounded-lg border border-gray-600"
                                        />
                                    </div>
                                    <div className="flex flex-col space-y-3">
                                        <a
                                            href={finalGifUrl}
                                            download="gif-editado.gif"
                                            className="px-6 py-3 bg-gradient-to-r from-emerald-500 to-green-500 text-white font-bold rounded-lg shadow-lg hover:scale-105 transform transition-transform duration-300 ease-in-out flex items-center space-x-2"
                                        >
                                            <DownloadIcon className="w-5 h-5" />
                                            <span>Descargar GIF</span>
                                        </a>
                                        <button
                                            onClick={() => setFinalGifUrl(null)}
                                            className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded-lg font-medium transition-colors"
                                        >
                                            Ocultar
                                        </button>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                )}

                {/* Mensaje de error */}
                {error && (
                    <div className="w-full bg-red-900/50 border border-red-700 p-4 rounded-xl mb-6 flex items-center animate-fade-in-up">
                        <XCircleIcon className="w-5 h-5 mr-3 text-red-400 flex-shrink-0" />
                        <p className="text-red-300">{error}</p>
                    </div>
                )}
            </main>
        </div>
    );
};

export default App;






