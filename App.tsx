
import React, { useState, useCallback, useEffect } from 'react';
import { ImageUploader } from './components/ImageUploader';
import { LoadingSpinner } from './components/LoadingSpinner';
import { GPUSettings } from './components/GPUSettings';
import { ProcessingInfo } from './components/ProcessingInfo';
import { BackgroundTips } from './components/BackgroundTips';
import { DownloadIcon, SparklesIcon, XCircleIcon } from './components/Icons';
import {
    processGifBackgroundRemoval,
    processStaticImageBackgroundRemoval,
    initializeProcessors,
    disposeProcessors,
    ProcessingOptions,
    ProcessingInfo as ProcessingInfoType
} from './services/backgroundRemovalService';

const App: React.FC = () => {
    const [originalFile, setOriginalFile] = useState<File | null>(null);
    const [originalGifUrl, setOriginalGifUrl] = useState<string | null>(null);
    const [processedGifUrl, setProcessedGifUrl] = useState<string | null>(null);
    const [processingProgress, setProcessingProgress] = useState<number>(0);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const [processingInfo, setProcessingInfo] = useState<ProcessingInfoType | null>(null);
    const [isInitializing, setIsInitializing] = useState<boolean>(true);
    const [processingOptions, setProcessingOptions] = useState<ProcessingOptions>({
        method: 'auto',
        aiModel: 'general',
        quality: 'medium',
        feather: 0.1,
        threshold: 0.5
    });

    // Inicializar procesadores al cargar la app
    useEffect(() => {
        const initialize = async () => {
            try {
                await initializeProcessors();
            } catch (error) {
                console.warn('Error inicializando procesadores:', error);
            } finally {
                setIsInitializing(false);
            }
        };

        initialize();

        // Limpiar al desmontar
        return () => {
            disposeProcessors();
        };
    }, []);

    const handleFileSelect = (file: File) => {
        setOriginalFile(file);
        setOriginalGifUrl(URL.createObjectURL(file));
        setProcessedGifUrl(null);
        setProcessingProgress(0);
        setProcessingInfo(null);
        setError(null);
    };

    const handleProcessClick = useCallback(async () => {
        if (!originalFile) return;

        setIsLoading(true);
        setError(null);
        setProcessedGifUrl(null);
        setProcessingProgress(0);
        setProcessingInfo(null);

        try {
            let result;

            // Detectar tipo de archivo y usar la función apropiada
            if (originalFile.type === 'image/gif') {
                console.log('Procesando como GIF...');
                result = await processGifBackgroundRemoval(
                    originalFile,
                    (progress) => setProcessingProgress(progress),
                    processingOptions
                );
            } else {
                console.log('Procesando como imagen estática...');
                result = await processStaticImageBackgroundRemoval(
                    originalFile,
                    (progress) => setProcessingProgress(progress),
                    processingOptions
                );
            }

            const processedUrl = URL.createObjectURL(result.blob);
            setProcessedGifUrl(processedUrl);
            setProcessingInfo(result.info);
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Error desconocido.';
            setError(`Error en el procesamiento: ${errorMessage}`);
        } finally {
            setIsLoading(false);
        }
    }, [originalFile, processingOptions]);

    const handleReset = () => {
        setOriginalFile(null);
        setOriginalGifUrl(null);
        setProcessedGifUrl(null);
        setProcessingProgress(0);
        setProcessingInfo(null);
        setError(null);
        setIsLoading(false);
    };
    
    if (isInitializing) {
        return (
            <div className="min-h-screen bg-gray-900 text-gray-100 flex flex-col items-center justify-center">
                <LoadingSpinner />
                <p className="mt-4 text-gray-400">Inicializando procesadores de GPU...</p>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-900 text-gray-100 flex flex-col items-center p-4 sm:p-6 lg:p-8">
            <header className="w-full max-w-5xl text-center mb-8">
                <h1 className="text-4xl sm:text-5xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-cyan-400 mb-2">
                    Removedor de Fondo con GPU
                </h1>
                <p className="text-lg text-gray-400">
                    Sube una imagen (GIF, JPG, PNG) y usaremos tu tarjeta gráfica para quitar el fondo.
                </p>
            </header>

            <main className="w-full max-w-5xl flex-grow">
                {!originalGifUrl ? (
                    <>
                        <GPUSettings
                            options={processingOptions}
                            onOptionsChange={setProcessingOptions}
                            isProcessing={isLoading}
                        />
                        <BackgroundTips />
                        <ImageUploader onFileSelect={handleFileSelect} />
                    </>
                ) : (
                    <div className="w-full flex flex-col items-center">
                        <GPUSettings
                            options={processingOptions}
                            onOptionsChange={setProcessingOptions}
                            isProcessing={isLoading}
                        />

                        <ProcessingInfo
                            info={processingInfo}
                            isVisible={!!processingInfo}
                        />
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 w-full mb-6">
                            <div className="flex flex-col items-center bg-gray-800 p-4 rounded-xl border border-gray-700">
                                <h2 className="text-xl font-bold mb-3 text-gray-300">Original</h2>
                                <img src={originalGifUrl} alt="Original GIF" className="max-w-full h-auto max-h-80 rounded-lg shadow-lg"/>
                            </div>
                            <div className="flex flex-col items-center bg-gray-800 p-4 rounded-xl border border-gray-700 min-h-[200px] justify-center">
                                <h2 className="text-xl font-bold mb-3 text-gray-300">Resultado Transparente</h2>
                                {isLoading ? (
                                    <div className="flex flex-col items-center">
                                        <LoadingSpinner />
                                        <p className="mt-2 text-cyan-400">Procesando frames: {processingProgress}%</p>
                                        <div className="w-64 bg-gray-700 rounded-full h-2 mt-2">
                                            <div 
                                                className="bg-gradient-to-r from-purple-500 to-cyan-500 h-2 rounded-full transition-all duration-300"
                                                style={{ width: `${processingProgress}%` }}
                                            ></div>
                                        </div>
                                    </div>
                                ) : processedGifUrl ? (
                                    <img src={processedGifUrl} alt="GIF con fondo transparente" className="max-w-full h-auto max-h-80 rounded-lg bg-[url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCI+PHJlY3Qgd2lkdGg9IjEwIiBoZWlnaHQ9IjEwIiBmaWxsPSIjNDc1NTY5Ij48L3JlY3Q+PHJlY3QgeD0iMTAiIHk9IjEwIiB3aWR0aD0iMTAiIGhlaWdodD0iMTAiIGZpbGw9IiM0NzU1NjkiPjwvcmVjdD48cmVjdCB5PSIxMCIgd2lkdGg9IjEwIiBoZWlnaHQ9IjEwIiBmaWxsPSIjNTc2NDdiIj48L3JlY3Q+PHJlY3QgeD0iMTAiIHdpZHRoPSIxMCIgaGVpZ2h0PSIxMCIgZmlsbD0iIzU3NjQ3YiI+PC9yZWN0Pjwvc3ZnPg==')] bg-center shadow-lg" />
                                ) : (
                                     <p className="text-gray-500">Haz clic en "Procesar" para empezar.</p>
                                )}
                            </div>
                        </div>

                        {error && (
                            <div className="w-full bg-red-900/50 border border-red-700 p-4 rounded-xl mb-6 flex items-center animate-fade-in-up">
                                <XCircleIcon className="w-5 h-5 mr-3 text-red-400 flex-shrink-0" />
                                <p className="text-red-300">{error}</p>
                            </div>
                        )}

                        <div className="flex flex-wrap gap-4 items-center justify-center">
                             {!isLoading && (
                                <button
                                    onClick={handleProcessClick}
                                    disabled={isLoading}
                                    className="px-8 py-3 bg-gradient-to-r from-purple-500 to-cyan-500 text-white font-bold rounded-full shadow-lg hover:scale-105 transform transition-transform duration-300 ease-in-out disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                                >
                                    <SparklesIcon className="w-6 h-6 mr-2" />
                                    Procesar Imagen
                                </button>
                            )}

                             {processedGifUrl && !isLoading && (
                                <a
                                    href={processedGifUrl}
                                    download="processed-gif.gif"
                                    className="px-8 py-3 bg-gradient-to-r from-emerald-500 to-green-500 text-white font-bold rounded-full shadow-lg hover:scale-105 transform transition-transform duration-300 ease-in-out flex items-center"
                                >
                                    <DownloadIcon className="w-6 h-6 mr-2" />
                                    Descargar Resultado
                                </a>
                            )}
                            
                            <button
                                onClick={handleReset}
                                className="px-6 py-2 bg-gray-700 text-gray-300 font-semibold rounded-full hover:bg-gray-600 transition-colors"
                            >
                                Subir otro
                            </button>
                        </div>
                    </div>
                )}
            </main>
        </div>
    );
};

export default App;






