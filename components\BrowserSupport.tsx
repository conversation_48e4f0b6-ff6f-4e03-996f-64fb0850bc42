import React, { useState, useEffect } from 'react';

interface BrowserSupportInfo {
    hasFileSystemAccess: boolean;
    hasShowDirectoryPicker: boolean;
    browserName: string;
    browserVersion: string;
    isSupported: boolean;
}

export const BrowserSupport: React.FC = () => {
    const [supportInfo, setSupportInfo] = useState<BrowserSupportInfo | null>(null);
    const [isExpanded, setIsExpanded] = useState(false);

    useEffect(() => {
        const checkSupport = () => {
            const userAgent = navigator.userAgent;
            let browserName = 'Desconocido';
            let browserVersion = 'Desconocido';

            // Detectar navegador
            if (userAgent.includes('Chrome')) {
                browserName = 'Chrome';
                const match = userAgent.match(/Chrome\/(\d+)/);
                browserVersion = match ? match[1] : 'Desconocido';
            } else if (userAgent.includes('Edge')) {
                browserName = 'Edge';
                const match = userAgent.match(/Edg\/(\d+)/);
                browserVersion = match ? match[1] : 'Desconocido';
            } else if (userAgent.includes('Firefox')) {
                browserName = 'Firefox';
                const match = userAgent.match(/Firefox\/(\d+)/);
                browserVersion = match ? match[1] : 'Desconocido';
            } else if (userAgent.includes('Safari')) {
                browserName = 'Safari';
                const match = userAgent.match(/Version\/(\d+)/);
                browserVersion = match ? match[1] : 'Desconocido';
            }

            const hasFileSystemAccess = 'showDirectoryPicker' in window;
            const hasShowDirectoryPicker = typeof (window as any).showDirectoryPicker === 'function';
            
            const isSupported = hasFileSystemAccess && hasShowDirectoryPicker;

            setSupportInfo({
                hasFileSystemAccess,
                hasShowDirectoryPicker,
                browserName,
                browserVersion,
                isSupported
            });
        };

        checkSupport();
    }, []);

    if (!supportInfo) {
        return null;
    }

    const getSupportIcon = () => {
        if (supportInfo.isSupported) return '✅';
        return '❌';
    };

    const getSupportColor = () => {
        if (supportInfo.isSupported) return 'text-green-400';
        return 'text-red-400';
    };

    const getSupportMessage = () => {
        if (supportInfo.isSupported) {
            return 'Tu navegador soporta selección de carpetas';
        }
        return 'Tu navegador NO soporta selección de carpetas';
    };

    return (
        <div className={`rounded-xl p-4 border mb-4 ${
            supportInfo.isSupported 
                ? 'bg-green-900/30 border-green-700' 
                : 'bg-red-900/30 border-red-700'
        }`}>
            <div 
                className="flex items-center justify-between cursor-pointer"
                onClick={() => setIsExpanded(!isExpanded)}
            >
                <div className="flex items-center space-x-3">
                    <span className="text-2xl">{getSupportIcon()}</span>
                    <div>
                        <h3 className={`text-lg font-semibold ${getSupportColor()}`}>
                            Compatibilidad del Navegador
                        </h3>
                        <p className="text-sm text-gray-400">
                            {getSupportMessage()}
                        </p>
                    </div>
                </div>
                <svg 
                    className={`w-5 h-5 text-gray-400 transition-transform ${isExpanded ? 'rotate-180' : ''}`}
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
            </div>

            {isExpanded && (
                <div className="mt-4 space-y-4 border-t border-gray-700 pt-4">
                    {/* Información del navegador */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="bg-gray-800/50 rounded-lg p-3">
                            <h4 className="font-semibold text-gray-200 mb-2">Navegador Detectado</h4>
                            <p className="text-gray-300">{supportInfo.browserName} {supportInfo.browserVersion}</p>
                        </div>

                        <div className="bg-gray-800/50 rounded-lg p-3">
                            <h4 className="font-semibold text-gray-200 mb-2">APIs Disponibles</h4>
                            <div className="space-y-1 text-sm">
                                <div className="flex items-center space-x-2">
                                    <span>{supportInfo.hasFileSystemAccess ? '✅' : '❌'}</span>
                                    <span>File System Access API</span>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <span>{supportInfo.hasShowDirectoryPicker ? '✅' : '❌'}</span>
                                    <span>showDirectoryPicker</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Recomendaciones */}
                    {!supportInfo.isSupported && (
                        <div className="bg-yellow-900/30 border border-yellow-700 rounded-lg p-4">
                            <h4 className="font-semibold text-yellow-200 mb-2 flex items-center">
                                <span className="mr-2">💡</span>
                                Recomendaciones
                            </h4>
                            <div className="text-sm text-yellow-300 space-y-2">
                                <p><strong>Para usar selección de carpetas, necesitas:</strong></p>
                                <ul className="list-disc list-inside ml-4 space-y-1">
                                    <li>Google Chrome 86+ o Microsoft Edge 86+</li>
                                    <li>Navegador actualizado a la versión más reciente</li>
                                    <li>Conexión HTTPS (o localhost para desarrollo)</li>
                                </ul>
                                <p className="mt-2">
                                    <strong>Alternativa:</strong> Los archivos se descargarán a tu carpeta de Descargas.
                                </p>
                            </div>
                        </div>
                    )}

                    {/* Información adicional */}
                    <div className="bg-blue-900/30 border border-blue-700 rounded-lg p-4">
                        <h4 className="font-semibold text-blue-200 mb-2 flex items-center">
                            <span className="mr-2">ℹ️</span>
                            Información Técnica
                        </h4>
                        <div className="text-sm text-blue-300 space-y-1">
                            <p><strong>User Agent:</strong> {navigator.userAgent.substring(0, 100)}...</p>
                            <p><strong>Plataforma:</strong> {navigator.platform}</p>
                            <p><strong>Idioma:</strong> {navigator.language}</p>
                            <p><strong>Conexión segura:</strong> {location.protocol === 'https:' ? 'Sí' : 'No'}</p>
                        </div>
                    </div>

                    {/* Botón de prueba */}
                    <div className="flex justify-center">
                        <button
                            onClick={async () => {
                                try {
                                    if ('showDirectoryPicker' in window) {
                                        // @ts-ignore
                                        const dirHandle = await window.showDirectoryPicker();
                                        alert(`✅ Prueba exitosa!\n\nCarpeta seleccionada: ${dirHandle.name}`);
                                    } else {
                                        alert('❌ showDirectoryPicker no está disponible en tu navegador');
                                    }
                                } catch (error) {
                                    if (error instanceof Error && error.name === 'AbortError') {
                                        alert('Prueba cancelada por el usuario');
                                    } else {
                                        alert(`❌ Error en la prueba: ${error instanceof Error ? error.message : 'Error desconocido'}`);
                                    }
                                }
                            }}
                            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
                        >
                            🧪 Probar Selección de Carpeta
                        </button>
                    </div>
                </div>
            )}
        </div>
    );
};
