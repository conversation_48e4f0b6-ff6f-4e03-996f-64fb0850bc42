const vertexShaderSource = `
    attribute vec2 a_position;
    attribute vec2 a_texCoord;
    varying vec2 v_texCoord;
    
    void main() {
        gl_Position = vec4(a_position, 0.0, 1.0);
        v_texCoord = a_texCoord;
    }
`;

const fragmentShaderSource = `
    precision mediump float;
    uniform sampler2D u_image;
    uniform vec2 u_resolution;
    varying vec2 v_texCoord;

    // Función para detectar si un color es fondo (versión conservadora)
    float isBackground(vec3 color) {
        // Detección de chroma key (verde/azul) - más estricta
        float greenScreen = step(0.6, color.g) * step(color.r, 0.25) * step(color.b, 0.25);
        float blueScreen = step(0.6, color.b) * step(color.r, 0.25) * step(color.g, 0.25);

        // Detección de fondo blanco muy claro
        float grayValue = (color.r + color.g + color.b) / 3.0;
        float isGray = 1.0 - step(0.08, abs(color.r - grayValue)) * step(0.08, abs(color.g - grayValue)) * step(0.08, abs(color.b - grayValue));
        float whiteBackground = isGray * step(0.85, grayValue);

        // Detección de fondo negro muy oscuro
        float blackBackground = step(grayValue, 0.06);

        // Solo detectar fondos muy obvios
        return max(max(max(greenScreen, blueScreen), whiteBackground), blackBackground);
    }

    // Función para detectar bordes y suavizar
    float edgeDetection(vec2 coord) {
        vec2 texelSize = 1.0 / u_resolution;
        float backgroundCount = 0.0;
        float totalSamples = 0.0;

        // Muestrear píxeles vecinos en un radio pequeño
        for(float x = -1.0; x <= 1.0; x += 1.0) {
            for(float y = -1.0; y <= 1.0; y += 1.0) {
                vec2 sampleCoord = coord + vec2(x, y) * texelSize;
                if(sampleCoord.x >= 0.0 && sampleCoord.x <= 1.0 && sampleCoord.y >= 0.0 && sampleCoord.y <= 1.0) {
                    vec3 sampleColor = texture2D(u_image, sampleCoord).rgb;
                    backgroundCount += isBackground(sampleColor);
                    totalSamples += 1.0;
                }
            }
        }

        return backgroundCount / totalSamples;
    }

    void main() {
        vec4 color = texture2D(u_image, v_texCoord);

        // Detectar si el píxel actual es fondo
        float currentIsBackground = isBackground(color.rgb);

        // Detectar bordes para suavizado
        float edgeStrength = edgeDetection(v_texCoord);

        // Calcular alpha final
        float alpha = color.a;

        if(currentIsBackground > 0.5) {
            // Es definitivamente fondo - hacer transparente
            alpha = 0.0;
        } else if(edgeStrength > 0.3) {
            // Está cerca del fondo - suavizar
            alpha = color.a * (1.0 - edgeStrength * 0.7);
        }

        gl_FragColor = vec4(color.rgb, alpha);
    }
`;

export const removeBackgroundWebGL = async (imageData: ImageData): Promise<ImageData> => {
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    
    if (!gl) {
        throw new Error('WebGL no está disponible');
    }
    
    canvas.width = imageData.width;
    canvas.height = imageData.height;
    
    // Crear shaders
    const vertexShader = createShader(gl, gl.VERTEX_SHADER, vertexShaderSource);
    const fragmentShader = createShader(gl, gl.FRAGMENT_SHADER, fragmentShaderSource);
    
    if (!vertexShader || !fragmentShader) {
        throw new Error('Error creando shaders');
    }
    
    // Crear programa
    const program = createProgram(gl, vertexShader, fragmentShader);
    if (!program) {
        throw new Error('Error creando programa WebGL');
    }
    
    // Configurar geometría
    const positionBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, new Float32Array([
        -1, -1,  1, -1,  -1, 1,
        -1, 1,   1, -1,   1, 1
    ]), gl.STATIC_DRAW);
    
    const texCoordBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, texCoordBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, new Float32Array([
        0, 1,  1, 1,  0, 0,
        0, 0,  1, 1,  1, 0
    ]), gl.STATIC_DRAW);
    
    // Crear textura
    const texture = gl.createTexture();
    gl.bindTexture(gl.TEXTURE_2D, texture);
    gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, gl.RGBA, gl.UNSIGNED_BYTE, imageData);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.LINEAR);

    // Renderizar
    gl.useProgram(program);
    gl.viewport(0, 0, canvas.width, canvas.height);

    const positionLocation = gl.getAttribLocation(program, 'a_position');
    const texCoordLocation = gl.getAttribLocation(program, 'a_texCoord');
    const resolutionLocation = gl.getUniformLocation(program, 'u_resolution');
    
    gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
    gl.enableVertexAttribArray(positionLocation);
    gl.vertexAttribPointer(positionLocation, 2, gl.FLOAT, false, 0, 0);

    gl.bindBuffer(gl.ARRAY_BUFFER, texCoordBuffer);
    gl.enableVertexAttribArray(texCoordLocation);
    gl.vertexAttribPointer(texCoordLocation, 2, gl.FLOAT, false, 0, 0);

    // Pasar resolución como uniform
    gl.uniform2f(resolutionLocation, canvas.width, canvas.height);

    gl.drawArrays(gl.TRIANGLES, 0, 6);
    
    // Obtener resultado
    const ctx = document.createElement('canvas').getContext('2d');
    if (!ctx) {
        throw new Error('Error creando contexto 2D');
    }
    
    ctx.canvas.width = canvas.width;
    ctx.canvas.height = canvas.height;
    ctx.drawImage(canvas, 0, 0);
    
    return ctx.getImageData(0, 0, canvas.width, canvas.height);
};

function createShader(gl: WebGLRenderingContext, type: number, source: string): WebGLShader | null {
    const shader = gl.createShader(type);
    if (!shader) return null;
    
    gl.shaderSource(shader, source);
    gl.compileShader(shader);
    
    if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
        console.error('Error compilando shader:', gl.getShaderInfoLog(shader));
        gl.deleteShader(shader);
        return null;
    }
    
    return shader;
}

function createProgram(gl: WebGLRenderingContext, vertexShader: WebGLShader, fragmentShader: WebGLShader): WebGLProgram | null {
    const program = gl.createProgram();
    if (!program) return null;
    
    gl.attachShader(program, vertexShader);
    gl.attachShader(program, fragmentShader);
    gl.linkProgram(program);
    
    if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
        console.error('Error enlazando programa:', gl.getProgramInfoLog(program));
        gl.deleteProgram(program);
        return null;
    }
    
    return program;
}
