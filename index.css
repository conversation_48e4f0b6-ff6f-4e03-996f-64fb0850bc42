/* Estilos personalizados para la aplicación */

/* Animaciones */
@keyframes fade-in-up {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in-up {
    animation: fade-in-up 0.5s ease-out;
}

/* Estilos para sliders */
.slider {
    -webkit-appearance: none;
    appearance: none;
    background: transparent;
    cursor: pointer;
}

.slider::-webkit-slider-track {
    background: #374151;
    height: 8px;
    border-radius: 4px;
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    background: linear-gradient(45deg, #8b5cf6, #06b6d4);
    height: 20px;
    width: 20px;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
}

.slider::-webkit-slider-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.slider::-moz-range-track {
    background: #374151;
    height: 8px;
    border-radius: 4px;
    border: none;
}

.slider::-moz-range-thumb {
    background: linear-gradient(45deg, #8b5cf6, #06b6d4);
    height: 20px;
    width: 20px;
    border-radius: 50%;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
}

.slider::-moz-range-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* Estilos para el loading spinner */
.spinner {
    border: 4px solid #374151;
    border-top: 4px solid #8b5cf6;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Estilos para botones con gradiente */
.btn-gradient {
    background: linear-gradient(45deg, #8b5cf6, #06b6d4);
    transition: all 0.3s ease;
}

.btn-gradient:hover {
    background: linear-gradient(45deg, #7c3aed, #0891b2);
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

/* Estilos para el drag and drop */
.drag-area {
    border: 2px dashed #6b7280;
    transition: all 0.3s ease;
}

.drag-area.drag-over {
    border-color: #8b5cf6;
    background-color: rgba(139, 92, 246, 0.1);
}

/* Estilos para las tarjetas de información */
.info-card {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    border: 1px solid #374151;
    transition: all 0.3s ease;
}

.info-card:hover {
    border-color: #6b7280;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Estilos para el progreso */
.progress-bar {
    background: linear-gradient(90deg, #8b5cf6, #06b6d4);
    transition: width 0.3s ease;
}

/* Estilos responsivos */
@media (max-width: 768px) {
    .grid-responsive {
        grid-template-columns: 1fr;
    }
    
    .text-responsive {
        font-size: 1.5rem;
    }
}

/* Estilos para tooltips */
.tooltip {
    position: relative;
}

.tooltip::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #1f2937;
    color: #f9fafb;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
}

.tooltip:hover::before {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-8px);
}

/* Estilos para el estado de carga */
.loading-overlay {
    background: rgba(17, 24, 39, 0.8);
    backdrop-filter: blur(4px);
}

/* Estilos para mensajes de error */
.error-message {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: #fca5a5;
}

/* Estilos para mensajes de éxito */
.success-message {
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.3);
    color: #86efac;
}

/* Estilos para el modo oscuro mejorado */
.dark-mode {
    background: radial-gradient(ellipse at top, #1f2937 0%, #111827 100%);
    min-height: 100vh;
}

/* Efectos de glassmorphism */
.glass {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Estilos para iconos animados */
.icon-bounce {
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -8px, 0);
    }
    70% {
        transform: translate3d(0, -4px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}
