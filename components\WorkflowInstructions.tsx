import React, { useState } from 'react';

export const WorkflowInstructions: React.FC = () => {
    const [isExpanded, setIsExpanded] = useState(false);

    return (
        <div className="bg-blue-900/30 border border-blue-700 rounded-xl p-4 mb-6">
            <div 
                className="flex items-center justify-between cursor-pointer"
                onClick={() => setIsExpanded(!isExpanded)}
            >
                <div className="flex items-center space-x-3">
                    <span className="text-2xl">🎯</span>
                    <div>
                        <h3 className="text-lg font-semibold text-blue-200">
                            Cómo Usar el Editor de Frames
                        </h3>
                        <p className="text-sm text-blue-300">
                            Flujo de trabajo paso a paso
                        </p>
                    </div>
                </div>
                <svg 
                    className={`w-5 h-5 text-blue-400 transition-transform ${isExpanded ? 'rotate-180' : ''}`}
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
            </div>

            {isExpanded && (
                <div className="mt-4 space-y-4 border-t border-blue-700 pt-4">
                    {/* Pasos del flujo de trabajo */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div className="bg-blue-800/50 rounded-lg p-4">
                            <div className="text-2xl mb-2">1️⃣</div>
                            <h4 className="font-semibold text-blue-200 mb-2">Subir GIF</h4>
                            <p className="text-sm text-blue-300">
                                Arrastra tu GIF o haz clic para seleccionarlo. 
                                Se extraerán automáticamente todos los frames.
                            </p>
                        </div>

                        <div className="bg-orange-800/50 rounded-lg p-4">
                            <div className="text-2xl mb-2">2️⃣</div>
                            <h4 className="font-semibold text-orange-200 mb-2">Exportar Frames</h4>
                            <p className="text-sm text-orange-300">
                                Haz clic en "Exportar Todos" para descargar 
                                todos los frames como archivos PNG.
                            </p>
                        </div>

                        <div className="bg-emerald-800/50 rounded-lg p-4">
                            <div className="text-2xl mb-2">3️⃣</div>
                            <h4 className="font-semibold text-emerald-200 mb-2">Editar Externamente</h4>
                            <p className="text-sm text-emerald-300">
                                Usa "Abrir Carpeta" para encontrar los archivos.
                                Edita con tu programa favorito.
                            </p>
                        </div>

                        <div className="bg-purple-800/50 rounded-lg p-4">
                            <div className="text-2xl mb-2">4️⃣</div>
                            <h4 className="font-semibold text-purple-200 mb-2">Reconstruir GIF</h4>
                            <p className="text-sm text-purple-300">
                                Importa los archivos editados y 
                                haz clic en "Juntar GIF".
                            </p>
                        </div>
                    </div>

                    {/* Atajos de teclado */}
                    <div className="bg-gray-800/50 rounded-lg p-4">
                        <h4 className="font-semibold text-gray-200 mb-3 flex items-center">
                            <span className="mr-2">⌨️</span>
                            Atajos Útiles
                        </h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                            <div className="flex items-center space-x-2">
                                <kbd className="px-2 py-1 bg-gray-700 rounded text-xs">Ctrl+J</kbd>
                                <span className="text-gray-300">Abrir carpeta de Descargas (Chrome)</span>
                            </div>
                            <div className="flex items-center space-x-2">
                                <kbd className="px-2 py-1 bg-gray-700 rounded text-xs">Ctrl+Click</kbd>
                                <span className="text-gray-300">Seleccionar múltiples frames</span>
                            </div>
                            <div className="flex items-center space-x-2">
                                <kbd className="px-2 py-1 bg-gray-700 rounded text-xs">Click derecho</kbd>
                                <span className="text-gray-300">Menú contextual en frames</span>
                            </div>
                            <div className="flex items-center space-x-2">
                                <kbd className="px-2 py-1 bg-gray-700 rounded text-xs">Win+E</kbd>
                                <span className="text-gray-300">Abrir Explorador de Windows</span>
                            </div>
                        </div>
                    </div>

                    {/* Programas recomendados */}
                    <div className="bg-indigo-800/50 rounded-lg p-4">
                        <h4 className="font-semibold text-indigo-200 mb-3 flex items-center">
                            <span className="mr-2">🛠️</span>
                            Programas Recomendados para Editar
                        </h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3 text-sm">
                            <div className="text-indigo-300">
                                <strong>Profesionales:</strong>
                                <br />• Adobe Photoshop
                                <br />• Adobe Illustrator
                                <br />• Affinity Photo
                            </div>
                            <div className="text-indigo-300">
                                <strong>Gratuitos:</strong>
                                <br />• GIMP
                                <br />• Paint.NET
                                <br />• Krita
                            </div>
                            <div className="text-indigo-300">
                                <strong>Online:</strong>
                                <br />• Photopea.com
                                <br />• Remove.bg
                                <br />• Canva
                            </div>
                            <div className="text-indigo-300">
                                <strong>Automáticos:</strong>
                                <br />• Remove.bg
                                <br />• Background Eraser
                                <br />• Clipping Magic
                            </div>
                        </div>
                    </div>

                    {/* Consejos importantes */}
                    <div className="bg-yellow-800/50 rounded-lg p-4">
                        <h4 className="font-semibold text-yellow-200 mb-3 flex items-center">
                            <span className="mr-2">💡</span>
                            Consejos Importantes
                        </h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm text-yellow-300">
                            <div>
                                <strong>Al editar:</strong>
                                <ul className="list-disc list-inside mt-1 space-y-1">
                                    <li>Mantén el mismo nombre de archivo</li>
                                    <li>No cambies las dimensiones</li>
                                    <li>Guarda en formato PNG</li>
                                    <li>Preserva la transparencia</li>
                                </ul>
                            </div>
                            <div>
                                <strong>Para mejores resultados:</strong>
                                <ul className="list-disc list-inside mt-1 space-y-1">
                                    <li>Usa fondos uniformes en el GIF original</li>
                                    <li>Edita frame por frame para consistencia</li>
                                    <li>Verifica la transparencia antes de importar</li>
                                    <li>Haz backup del GIF original</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    {/* Solución de problemas */}
                    <div className="bg-red-800/50 rounded-lg p-4">
                        <h4 className="font-semibold text-red-200 mb-3 flex items-center">
                            <span className="mr-2">🔧</span>
                            Solución de Problemas
                        </h4>
                        <div className="text-sm text-red-300 space-y-2">
                            <div>
                                <strong>¿No encuentras los archivos?</strong>
                                <br />• Revisa tu carpeta de Descargas
                                <br />• Busca archivos que empiecen con "gif-editor-"
                                <br />• Usa Ctrl+J en Chrome para abrir Descargas
                            </div>
                            <div>
                                <strong>¿El GIF final se ve mal?</strong>
                                <br />• Verifica que todos los frames tengan las mismas dimensiones
                                <br />• Asegúrate de que la transparencia esté bien aplicada
                                <br />• Prueba con menos frames para depurar
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};
