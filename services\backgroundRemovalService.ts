import { extractFramesFromGif, createGifFromFrames } from '../utils/gifUtils';
import { removeBackgroundWebGL } from '../utils/webglBackgroundRemoval';

export const processGifBackgroundRemoval = async (
    gifFile: File,
    onProgress: (progress: number) => void
): Promise<Blob> => {
    try {
        // Paso 1: Extraer frames del GIF
        onProgress(10);
        const frames = await extractFramesFromGif(gifFile);
        
        // Paso 2: Procesar cada frame para quitar el fondo
        const processedFrames: ImageData[] = [];
        
        for (let i = 0; i < frames.length; i++) {
            const processedFrame = await removeBackgroundWebGL(frames[i].imageData);
            processedFrames.push(processedFrame);
            
            const progress = 10 + ((i + 1) / frames.length) * 80;
            onProgress(Math.round(progress));
        }
        
        // Paso 3: Recrear el GIF con los frames procesados
        onProgress(95);
        const processedGif = await createGifFromFrames(processedFrames, frames[0].delay);
        
        onProgress(100);
        return processedGif;
        
    } catch (error) {
        throw new Error(`Error procesando GIF: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
};
