import { extractFramesFromGif, createGifFromFrames } from '../utils/gifUtils';
import { removeBackgroundWebGL } from '../utils/webglBackgroundRemoval';
import { WebGPUBackgroundRemover } from '../utils/webgpuBackgroundRemoval';
import { AIBackgroundRemover } from '../utils/aiBackgroundRemoval';
import { detectGPUCapabilities, getBestProcessingMethod, formatGPUInfo } from '../utils/gpuDetection';

export interface ProcessingOptions {
    method: 'auto' | 'ai' | 'webgpu' | 'webgl' | 'cpu';
    aiModel: 'selfie' | 'general' | 'portrait';
    quality: 'high' | 'medium' | 'low';
    feather: number;
    threshold: number;
}

export interface ProcessingInfo {
    gpuInfo: string;
    method: string;
    framesCount: number;
    processingTime: number;
}

let gpuCapabilities: any = null;
let webgpuRemover: WebGPUBackgroundRemover | null = null;
let aiRemover: AIBackgroundRemover | null = null;

export const initializeProcessors = async (): Promise<void> => {
    // Detectar capacidades de GPU
    gpuCapabilities = await detectGPUCapabilities();
    console.log('GPU detectada:', formatGPUInfo(gpuCapabilities));

    // Inicializar procesadores según disponibilidad
    if (gpuCapabilities.hasGPU && gpuCapabilities.info?.supportsWebGPU) {
        webgpuRemover = new WebGPUBackgroundRemover();
        const initialized = await webgpuRemover.initialize();
        if (!initialized) {
            webgpuRemover = null;
        }
    }

    // Inicializar IA
    aiRemover = new AIBackgroundRemover();
    const aiInitialized = await aiRemover.initialize({
        modelType: 'general',
        precision: 'medium',
        useGPU: gpuCapabilities.hasGPU
    });

    if (!aiInitialized) {
        aiRemover = null;
    }
};

export const processGifBackgroundRemoval = async (
    gifFile: File,
    onProgress: (progress: number) => void,
    options: ProcessingOptions = {
        method: 'auto',
        aiModel: 'general',
        quality: 'medium',
        feather: 0.1,
        threshold: 0.5
    }
): Promise<{ blob: Blob; info: ProcessingInfo }> => {
    const startTime = Date.now();

    try {
        // Inicializar procesadores si no están listos
        if (!gpuCapabilities) {
            await initializeProcessors();
        }

        // Paso 1: Extraer frames del GIF
        onProgress(5);
        const frames = await extractFramesFromGif(gifFile);
        console.log(`Extraídos ${frames.length} frames del GIF`);

        // Determinar método de procesamiento
        const processingMethod = options.method === 'auto' ?
            getBestProcessingMethod(gpuCapabilities) :
            options.method;

        onProgress(10);

        // Paso 2: Procesar cada frame para quitar el fondo
        const processedFrames: ImageData[] = [];

        for (let i = 0; i < frames.length; i++) {
            let processedFrame: ImageData;

            try {
                processedFrame = await processFrame(
                    frames[i].imageData,
                    processingMethod,
                    options
                );
            } catch (error) {
                console.warn(`Error procesando frame ${i}, usando fallback:`, error);
                processedFrame = await removeBackgroundWebGL(frames[i].imageData);
            }

            processedFrames.push(processedFrame);

            const progress = 10 + ((i + 1) / frames.length) * 80;
            onProgress(Math.round(progress));
        }

        // Paso 3: Recrear el GIF con los frames procesados
        onProgress(95);
        const processedGif = await createGifFromFrames(
            processedFrames,
            frames[0]?.delay || 100
        );

        onProgress(100);

        const processingTime = Date.now() - startTime;

        const info: ProcessingInfo = {
            gpuInfo: formatGPUInfo(gpuCapabilities),
            method: processingMethod,
            framesCount: frames.length,
            processingTime
        };

        return { blob: processedGif, info };

    } catch (error) {
        throw new Error(`Error procesando GIF: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
};

const processFrame = async (
    imageData: ImageData,
    method: string,
    options: ProcessingOptions
): Promise<ImageData> => {
    switch (method) {
        case 'ai':
            if (aiRemover) {
                return await aiRemover.processImage(imageData);
            }
            break;

        case 'webgpu':
            if (webgpuRemover) {
                return await webgpuRemover.processImage(imageData, {
                    threshold: options.threshold,
                    feather: options.feather
                });
            }
            break;

        case 'webgl':
            return await removeBackgroundWebGL(imageData);

        case 'cpu':
        default:
            return await processFrameCPU(imageData, options);
    }

    // Fallback a WebGL si el método preferido falla
    return await removeBackgroundWebGL(imageData);
};

const processFrameCPU = async (
    imageData: ImageData,
    options: ProcessingOptions
): Promise<ImageData> => {
    const { width, height } = imageData;
    const result = new ImageData(width, height);
    const data = imageData.data;
    const resultData = result.data;

    for (let i = 0; i < width * height; i++) {
        const pixelIndex = i * 4;
        const r = data[pixelIndex];
        const g = data[pixelIndex + 1];
        const b = data[pixelIndex + 2];
        const a = data[pixelIndex + 3];

        // Algoritmo mejorado de detección de fondo
        const isGreenScreen = g > 100 && r < 80 && b < 80;
        const isBlueScreen = b > 100 && r < 80 && g < 80;

        // Detección de fondo blanco/gris
        const grayValue = (r + g + b) / 3;
        const isGray = Math.abs(r - grayValue) < 20 &&
                      Math.abs(g - grayValue) < 20 &&
                      Math.abs(b - grayValue) < 20;
        const isWhiteBackground = isGray && grayValue > 180;

        const isBackground = isGreenScreen || isBlueScreen || isWhiteBackground;

        // Aplicar suavizado en los bordes
        let alpha = a;
        if (isBackground) {
            alpha = 0;
        } else {
            // Suavizar bordes cerca del fondo
            const edgeDistance = calculateEdgeDistance(i, width, height, data);
            if (edgeDistance < options.feather * 10) {
                alpha = Math.round(a * (edgeDistance / (options.feather * 10)));
            }
        }

        resultData[pixelIndex] = r;
        resultData[pixelIndex + 1] = g;
        resultData[pixelIndex + 2] = b;
        resultData[pixelIndex + 3] = alpha;
    }

    return result;
};

const calculateEdgeDistance = (
    index: number,
    width: number,
    height: number,
    data: Uint8ClampedArray
): number => {
    const x = index % width;
    const y = Math.floor(index / width);

    let minDistance = 10;

    // Verificar píxeles vecinos
    for (let dy = -2; dy <= 2; dy++) {
        for (let dx = -2; dx <= 2; dx++) {
            const nx = x + dx;
            const ny = y + dy;

            if (nx >= 0 && nx < width && ny >= 0 && ny < height) {
                const neighborIndex = (ny * width + nx) * 4;
                const nr = data[neighborIndex];
                const ng = data[neighborIndex + 1];
                const nb = data[neighborIndex + 2];

                const isNeighborBackground =
                    (ng > 100 && nr < 80 && nb < 80) || // Verde
                    (nb > 100 && nr < 80 && ng < 80) || // Azul
                    ((nr + ng + nb) / 3 > 180); // Blanco

                if (isNeighborBackground) {
                    const distance = Math.sqrt(dx * dx + dy * dy);
                    minDistance = Math.min(minDistance, distance);
                }
            }
        }
    }

    return minDistance;
};

export const getProcessingInfo = (): ProcessingInfo | null => {
    if (!gpuCapabilities) {
        return null;
    }

    return {
        gpuInfo: formatGPUInfo(gpuCapabilities),
        method: getBestProcessingMethod(gpuCapabilities),
        framesCount: 0,
        processingTime: 0
    };
};

export const disposeProcessors = (): void => {
    if (webgpuRemover) {
        webgpuRemover.dispose();
        webgpuRemover = null;
    }

    if (aiRemover) {
        aiRemover.dispose();
        aiRemover = null;
    }
};
