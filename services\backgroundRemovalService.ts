import { extractFramesFromGif, createGifFromFrames } from '../utils/gifUtils';
import { removeBackgroundWebGL } from '../utils/webglBackgroundRemoval';
import { WebGPUBackgroundRemover } from '../utils/webgpuBackgroundRemoval';
import { AIBackgroundRemover } from '../utils/aiBackgroundRemoval';
import { detectGPUCapabilities, getBestProcessingMethod, formatGPUInfo } from '../utils/gpuDetection';

export interface ProcessingOptions {
    method: 'auto' | 'ai' | 'webgpu' | 'webgl' | 'cpu' | 'debug';
    aiModel: 'selfie' | 'general' | 'portrait';
    quality: 'high' | 'medium' | 'low';
    feather: number;
    threshold: number;
}

export interface ProcessingInfo {
    gpuInfo: string;
    method: string;
    framesCount: number;
    processingTime: number;
}

let gpuCapabilities: any = null;
let webgpuRemover: WebGPUBackgroundRemover | null = null;
let aiRemover: AIBackgroundRemover | null = null;

export const initializeProcessors = async (): Promise<void> => {
    // Detectar capacidades de GPU
    gpuCapabilities = await detectGPUCapabilities();
    console.log('GPU detectada:', formatGPUInfo(gpuCapabilities));

    // Inicializar procesadores según disponibilidad
    if (gpuCapabilities.hasGPU && gpuCapabilities.info?.supportsWebGPU) {
        webgpuRemover = new WebGPUBackgroundRemover();
        const initialized = await webgpuRemover.initialize();
        if (!initialized) {
            webgpuRemover = null;
        }
    }

    // Inicializar IA
    aiRemover = new AIBackgroundRemover();
    const aiInitialized = await aiRemover.initialize({
        modelType: 'general',
        precision: 'medium',
        useGPU: gpuCapabilities.hasGPU
    });

    if (!aiInitialized) {
        aiRemover = null;
    }
};

// Función para procesar imágenes estáticas (JPG/PNG)
export const processStaticImageBackgroundRemoval = async (
    imageFile: File,
    onProgress: (progress: number) => void,
    options: ProcessingOptions = {
        method: 'auto',
        aiModel: 'general',
        quality: 'medium',
        feather: 0.1,
        threshold: 0.5
    }
): Promise<{ blob: Blob; info: ProcessingInfo }> => {
    const startTime = Date.now();

    try {
        // Inicializar procesadores si no están listos
        if (!gpuCapabilities) {
            await initializeProcessors();
        }

        onProgress(10);

        // Cargar imagen en canvas
        const imageData = await loadImageToImageData(imageFile);
        console.log(`Imagen cargada: ${imageData.width}x${imageData.height}`);

        onProgress(20);

        // Determinar método de procesamiento
        const processingMethod = options.method === 'auto' ?
            getBestProcessingMethod(gpuCapabilities) :
            options.method;

        console.log(`Procesando imagen estática con método: ${processingMethod}`);

        onProgress(30);

        // Procesar imagen
        let processedImageData: ImageData;

        if (options.method === 'debug') {
            processedImageData = await processFrameNoChange(imageData);
        } else {
            processedImageData = await processFrame(imageData, processingMethod, options);
        }

        onProgress(80);

        // Convertir resultado a PNG
        const resultBlob = await imageDataToBlob(processedImageData);

        onProgress(100);

        const processingTime = Date.now() - startTime;

        const info: ProcessingInfo = {
            gpuInfo: formatGPUInfo(gpuCapabilities),
            method: processingMethod,
            framesCount: 1,
            processingTime
        };

        return { blob: resultBlob, info };

    } catch (error) {
        throw new Error(`Error procesando imagen: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
};

export const processGifBackgroundRemoval = async (
    gifFile: File,
    onProgress: (progress: number) => void,
    options: ProcessingOptions = {
        method: 'auto',
        aiModel: 'general',
        quality: 'medium',
        feather: 0.1,
        threshold: 0.5
    }
): Promise<{ blob: Blob; info: ProcessingInfo }> => {
    const startTime = Date.now();

    try {
        // Inicializar procesadores si no están listos
        if (!gpuCapabilities) {
            await initializeProcessors();
        }

        // Paso 1: Extraer frames del GIF
        onProgress(5);
        console.log('Iniciando extracción de frames...');
        const frames = await extractFramesFromGif(gifFile);
        console.log(`Extraídos ${frames.length} frames del GIF`);

        if (frames.length === 0) {
            throw new Error('No se pudieron extraer frames del GIF');
        }

        // Log información de los frames
        frames.forEach((frame, index) => {
            console.log(`Frame ${index}: ${frame.imageData.width}x${frame.imageData.height}, delay: ${frame.delay}ms`);
        });

        // Determinar método de procesamiento
        const processingMethod = options.method === 'auto' ?
            getBestProcessingMethod(gpuCapabilities) :
            options.method;

        onProgress(10);

        // Paso 2: Procesar cada frame para quitar el fondo
        console.log(`Iniciando procesamiento con método: ${processingMethod}`);
        const processedFrames: ImageData[] = [];

        for (let i = 0; i < frames.length; i++) {
            console.log(`Procesando frame ${i + 1}/${frames.length}`);
            let processedFrame: ImageData;

            try {
                // MODO DEPURACIÓN: Usar processFrameNoChange temporalmente
                // para verificar que la extracción de frames funciona
                if (options.method === 'debug') {
                    processedFrame = await processFrameNoChange(frames[i].imageData);
                } else {
                    processedFrame = await processFrame(
                        frames[i].imageData,
                        processingMethod,
                        options
                    );
                }
                console.log(`Frame ${i + 1} procesado exitosamente`);
            } catch (error) {
                console.warn(`Error procesando frame ${i}, usando fallback:`, error);
                processedFrame = await processFrameNoChange(frames[i].imageData);
            }

            processedFrames.push(processedFrame);

            const progress = 10 + ((i + 1) / frames.length) * 80;
            onProgress(Math.round(progress));
        }

        console.log(`Procesamiento completado: ${processedFrames.length} frames procesados`);

        // Paso 3: Recrear el GIF con los frames procesados
        onProgress(95);
        const processedGif = await createGifFromFrames(
            processedFrames,
            frames[0]?.delay || 100
        );

        onProgress(100);

        const processingTime = Date.now() - startTime;

        const info: ProcessingInfo = {
            gpuInfo: formatGPUInfo(gpuCapabilities),
            method: processingMethod,
            framesCount: frames.length,
            processingTime
        };

        return { blob: processedGif, info };

    } catch (error) {
        throw new Error(`Error procesando GIF: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
};

const processFrame = async (
    imageData: ImageData,
    method: string,
    options: ProcessingOptions
): Promise<ImageData> => {
    console.log(`Procesando frame con método: ${method}`);

    // Para depuración: analizar el contenido del frame
    const { width, height } = imageData;
    const data = imageData.data;
    let totalPixels = width * height;
    let backgroundPixels = 0;

    // Contar píxeles que serían detectados como fondo
    for (let i = 0; i < totalPixels; i++) {
        const pixelIndex = i * 4;
        const r = data[pixelIndex];
        const g = data[pixelIndex + 1];
        const b = data[pixelIndex + 2];

        // Usar la misma lógica de detección
        const isGreenScreen = g > 150 && r < 60 && b < 60;
        const isBlueScreen = b > 150 && r < 60 && g < 60;
        const grayValue = (r + g + b) / 3;
        const isGray = Math.abs(r - grayValue) < 20 &&
                      Math.abs(g - grayValue) < 20 &&
                      Math.abs(b - grayValue) < 20;
        const isWhiteBackground = isGray && grayValue > 220;
        const isBlackBackground = grayValue < 15;

        if (isGreenScreen || isBlueScreen || isWhiteBackground || isBlackBackground) {
            backgroundPixels++;
        }
    }

    const backgroundPercentage = (backgroundPixels / totalPixels) * 100;
    console.log(`Frame analysis: ${backgroundPercentage.toFixed(1)}% detectado como fondo`);

    if (backgroundPercentage > 90) {
        console.warn('¡Advertencia! Se está detectando demasiado como fondo. El resultado puede ser completamente transparente.');
    }

    switch (method) {
        case 'ai':
            if (aiRemover) {
                return await aiRemover.processImage(imageData);
            }
            break;

        case 'webgpu':
            if (webgpuRemover) {
                return await webgpuRemover.processImage(imageData, {
                    threshold: options.threshold,
                    feather: options.feather
                });
            }
            break;

        case 'webgl':
            return await removeBackgroundWebGL(imageData);

        case 'cpu':
        default:
            return await processFrameCPU(imageData, options);
    }

    // Fallback a CPU si el método preferido falla
    console.log('Usando fallback CPU');
    return await processFrameCPU(imageData, options);
};

// Función de prueba que no modifica la imagen (para depuración)
const processFrameNoChange = async (imageData: ImageData): Promise<ImageData> => {
    console.log('Procesando frame sin cambios (modo depuración)');

    // Crear una copia exacta sin modificaciones
    const result = new ImageData(imageData.width, imageData.height);
    result.data.set(imageData.data);

    return result;
};

// Función para cargar imagen estática a ImageData
const loadImageToImageData = async (imageFile: File): Promise<ImageData> => {
    return new Promise((resolve, reject) => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        if (!ctx) {
            reject(new Error('No se pudo crear contexto de canvas'));
            return;
        }

        const img = new Image();
        img.onload = () => {
            canvas.width = img.width;
            canvas.height = img.height;

            ctx.drawImage(img, 0, 0);
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

            resolve(imageData);
        };

        img.onerror = (error) => {
            console.error('Error cargando imagen:', error);
            reject(new Error('Error cargando imagen'));
        };

        img.src = URL.createObjectURL(imageFile);
    });
};

// Función para convertir ImageData a Blob
const imageDataToBlob = async (imageData: ImageData): Promise<Blob> => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    if (!ctx) {
        throw new Error('No se pudo crear contexto de canvas');
    }

    canvas.width = imageData.width;
    canvas.height = imageData.height;
    ctx.putImageData(imageData, 0, 0);

    return new Promise((resolve, reject) => {
        canvas.toBlob((blob) => {
            if (blob) {
                resolve(blob);
            } else {
                reject(new Error('Error creando imagen'));
            }
        }, 'image/png');
    });
};

const processFrameCPU = async (
    imageData: ImageData,
    options: ProcessingOptions
): Promise<ImageData> => {
    const { width, height } = imageData;
    const result = new ImageData(width, height);
    const data = imageData.data;
    const resultData = result.data;

    // Función para detectar si un píxel es fondo (versión conservadora)
    const isBackgroundPixel = (r: number, g: number, b: number): boolean => {
        // Detección de chroma key (verde/azul) - más estricta
        const isGreenScreen = g > 150 && r < 60 && b < 60;
        const isBlueScreen = b > 150 && r < 60 && g < 60;

        // Detección de fondo blanco muy claro
        const grayValue = (r + g + b) / 3;
        const isGray = Math.abs(r - grayValue) < 20 &&
                      Math.abs(g - grayValue) < 20 &&
                      Math.abs(b - grayValue) < 20;
        const isWhiteBackground = isGray && grayValue > 220;

        // Detección de fondo negro muy oscuro
        const isBlackBackground = grayValue < 15;

        // Solo detectar fondos muy obvios para evitar eliminar el sujeto
        return isGreenScreen || isBlueScreen || isWhiteBackground || isBlackBackground;
    };

    // Primera pasada: detectar píxeles de fondo
    const backgroundMask = new Array(width * height);
    for (let i = 0; i < width * height; i++) {
        const pixelIndex = i * 4;
        const r = data[pixelIndex];
        const g = data[pixelIndex + 1];
        const b = data[pixelIndex + 2];

        backgroundMask[i] = isBackgroundPixel(r, g, b);
    }

    // Segunda pasada: aplicar suavizado y generar resultado
    for (let i = 0; i < width * height; i++) {
        const pixelIndex = i * 4;
        const r = data[pixelIndex];
        const g = data[pixelIndex + 1];
        const b = data[pixelIndex + 2];
        const a = data[pixelIndex + 3];

        let alpha = a;

        if (backgroundMask[i]) {
            // Es fondo - hacer transparente
            alpha = 0;
        } else {
            // No es fondo - verificar si está cerca de bordes para suavizar
            const edgeStrength = calculateEdgeStrength(i, width, height, backgroundMask);
            if (edgeStrength > 0.3) {
                alpha = Math.round(a * (1 - edgeStrength * 0.7));
            }
        }

        resultData[pixelIndex] = r;
        resultData[pixelIndex + 1] = g;
        resultData[pixelIndex + 2] = b;
        resultData[pixelIndex + 3] = alpha;
    }

    return result;
};

const calculateEdgeStrength = (
    index: number,
    width: number,
    height: number,
    backgroundMask: boolean[]
): number => {
    const x = index % width;
    const y = Math.floor(index / width);

    let backgroundCount = 0;
    let totalSamples = 0;

    // Verificar píxeles vecinos en un radio de 2
    for (let dy = -2; dy <= 2; dy++) {
        for (let dx = -2; dx <= 2; dx++) {
            const nx = x + dx;
            const ny = y + dy;

            if (nx >= 0 && nx < width && ny >= 0 && ny < height) {
                const neighborIndex = ny * width + nx;
                if (backgroundMask[neighborIndex]) {
                    backgroundCount++;
                }
                totalSamples++;
            }
        }
    }

    return backgroundCount / totalSamples;
};

export const getProcessingInfo = (): ProcessingInfo | null => {
    if (!gpuCapabilities) {
        return null;
    }

    return {
        gpuInfo: formatGPUInfo(gpuCapabilities),
        method: getBestProcessingMethod(gpuCapabilities),
        framesCount: 0,
        processingTime: 0
    };
};

export const disposeProcessors = (): void => {
    if (webgpuRemover) {
        webgpuRemover.dispose();
        webgpuRemover = null;
    }

    if (aiRemover) {
        aiRemover.dispose();
        aiRemover = null;
    }
};
