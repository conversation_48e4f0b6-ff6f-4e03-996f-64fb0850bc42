import { extractFramesFromGif, createGifFromFrames } from '../utils/gifUtils';
import { removeBackgroundWebGL } from '../utils/webglBackgroundRemoval';
import { WebGPUBackgroundRemover } from '../utils/webgpuBackgroundRemoval';
import { AIBackgroundRemover } from '../utils/aiBackgroundRemoval';
import { detectGPUCapabilities, getBestProcessingMethod, formatGPUInfo } from '../utils/gpuDetection';

export interface ProcessingOptions {
    method: 'auto' | 'ai' | 'webgpu' | 'webgl' | 'cpu' | 'debug' | 'visualize';
    aiModel: 'selfie' | 'general' | 'portrait';
    quality: 'high' | 'medium' | 'low';
    feather: number;
    threshold: number;
}

export interface ProcessingInfo {
    gpuInfo: string;
    method: string;
    framesCount: number;
    processingTime: number;
}

let gpuCapabilities: any = null;
let webgpuRemover: WebGPUBackgroundRemover | null = null;
let aiRemover: AIBackgroundRemover | null = null;

export const initializeProcessors = async (): Promise<void> => {
    // Detectar capacidades de GPU
    gpuCapabilities = await detectGPUCapabilities();
    console.log('GPU detectada:', formatGPUInfo(gpuCapabilities));

    // Inicializar procesadores según disponibilidad
    if (gpuCapabilities.hasGPU && gpuCapabilities.info?.supportsWebGPU) {
        webgpuRemover = new WebGPUBackgroundRemover();
        const initialized = await webgpuRemover.initialize();
        if (!initialized) {
            webgpuRemover = null;
        }
    }

    // Inicializar IA
    aiRemover = new AIBackgroundRemover();
    const aiInitialized = await aiRemover.initialize({
        modelType: 'general',
        precision: 'medium',
        useGPU: gpuCapabilities.hasGPU
    });

    if (!aiInitialized) {
        aiRemover = null;
    }
};

// Función para procesar imágenes estáticas (JPG/PNG)
export const processStaticImageBackgroundRemoval = async (
    imageFile: File,
    onProgress: (progress: number) => void,
    options: ProcessingOptions = {
        method: 'auto',
        aiModel: 'general',
        quality: 'medium',
        feather: 0.1,
        threshold: 0.5
    }
): Promise<{ blob: Blob; info: ProcessingInfo }> => {
    const startTime = Date.now();

    try {
        // Inicializar procesadores si no están listos
        if (!gpuCapabilities) {
            await initializeProcessors();
        }

        onProgress(10);

        // Cargar imagen en canvas
        const imageData = await loadImageToImageData(imageFile);
        console.log(`Imagen cargada: ${imageData.width}x${imageData.height}`);

        onProgress(20);

        // Determinar método de procesamiento
        const processingMethod = options.method === 'auto' ?
            getBestProcessingMethod(gpuCapabilities) :
            options.method;

        console.log(`Procesando imagen estática con método: ${processingMethod}`);

        onProgress(30);

        // Procesar imagen
        let processedImageData: ImageData;

        if (options.method === 'debug') {
            processedImageData = await processFrameNoChange(imageData);
        } else if (options.method === 'visualize') {
            processedImageData = await processFrameVisualize(imageData);
        } else {
            processedImageData = await processFrame(imageData, processingMethod, options);
        }

        onProgress(80);

        // Convertir resultado a PNG
        const resultBlob = await imageDataToBlob(processedImageData);

        onProgress(100);

        const processingTime = Date.now() - startTime;

        const info: ProcessingInfo = {
            gpuInfo: formatGPUInfo(gpuCapabilities),
            method: processingMethod,
            framesCount: 1,
            processingTime
        };

        return { blob: resultBlob, info };

    } catch (error) {
        throw new Error(`Error procesando imagen: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
};

export const processGifBackgroundRemoval = async (
    gifFile: File,
    onProgress: (progress: number) => void,
    options: ProcessingOptions = {
        method: 'auto',
        aiModel: 'general',
        quality: 'medium',
        feather: 0.1,
        threshold: 0.5
    }
): Promise<{ blob: Blob; info: ProcessingInfo }> => {
    const startTime = Date.now();

    try {
        // Inicializar procesadores si no están listos
        if (!gpuCapabilities) {
            await initializeProcessors();
        }

        // Paso 1: Extraer frames del GIF
        onProgress(5);
        console.log('Iniciando extracción de frames...');
        const frames = await extractFramesFromGif(gifFile);
        console.log(`Extraídos ${frames.length} frames del GIF`);

        if (frames.length === 0) {
            throw new Error('No se pudieron extraer frames del GIF');
        }

        // Log información de los frames
        frames.forEach((frame, index) => {
            console.log(`Frame ${index}: ${frame.imageData.width}x${frame.imageData.height}, delay: ${frame.delay}ms`);
        });

        // Determinar método de procesamiento
        const processingMethod = options.method === 'auto' ?
            getBestProcessingMethod(gpuCapabilities) :
            options.method;

        onProgress(10);

        // Paso 2: Procesar cada frame para quitar el fondo
        console.log(`Iniciando procesamiento con método: ${processingMethod}`);
        const processedFrames: ImageData[] = [];

        for (let i = 0; i < frames.length; i++) {
            console.log(`Procesando frame ${i + 1}/${frames.length}`);
            let processedFrame: ImageData;

            try {
                // MODOS ESPECIALES DE DEPURACIÓN
                if (options.method === 'debug') {
                    processedFrame = await processFrameNoChange(frames[i].imageData);
                } else if (options.method === 'visualize') {
                    processedFrame = await processFrameVisualize(frames[i].imageData);
                } else {
                    processedFrame = await processFrame(
                        frames[i].imageData,
                        processingMethod,
                        options
                    );
                }
                console.log(`Frame ${i + 1} procesado exitosamente`);
            } catch (error) {
                console.warn(`Error procesando frame ${i}, usando fallback:`, error);
                processedFrame = await processFrameNoChange(frames[i].imageData);
            }

            processedFrames.push(processedFrame);

            const progress = 10 + ((i + 1) / frames.length) * 80;
            onProgress(Math.round(progress));
        }

        console.log(`Procesamiento completado: ${processedFrames.length} frames procesados`);

        // Paso 3: Recrear el GIF con los frames procesados
        onProgress(95);
        console.log(`Iniciando reconstrucción de GIF con ${processedFrames.length} frames procesados`);

        const processedGif = await createGifFromFrames(
            processedFrames,
            frames[0]?.delay || 100
        );

        console.log(`GIF reconstruido exitosamente: ${processedGif.size} bytes`);
        onProgress(100);

        const processingTime = Date.now() - startTime;

        const info: ProcessingInfo = {
            gpuInfo: formatGPUInfo(gpuCapabilities),
            method: processingMethod,
            framesCount: frames.length,
            processingTime
        };

        return { blob: processedGif, info };

    } catch (error) {
        throw new Error(`Error procesando GIF: ${error instanceof Error ? error.message : 'Error desconocido'}`);
    }
};

const processFrame = async (
    imageData: ImageData,
    method: string,
    options: ProcessingOptions
): Promise<ImageData> => {
    console.log(`Procesando frame con método: ${method}`);

    // Para depuración: analizar el contenido del frame
    const { width, height } = imageData;
    const data = imageData.data;
    let totalPixels = width * height;
    let backgroundPixels = 0;

    // Contar píxeles que serían detectados como fondo
    for (let i = 0; i < totalPixels; i++) {
        const pixelIndex = i * 4;
        const r = data[pixelIndex];
        const g = data[pixelIndex + 1];
        const b = data[pixelIndex + 2];

        // Usar la misma lógica de detección
        const isGreenScreen = g > 150 && r < 60 && b < 60;
        const isBlueScreen = b > 150 && r < 60 && g < 60;
        const grayValue = (r + g + b) / 3;
        const isGray = Math.abs(r - grayValue) < 20 &&
                      Math.abs(g - grayValue) < 20 &&
                      Math.abs(b - grayValue) < 20;
        const isWhiteBackground = isGray && grayValue > 220;
        const isBlackBackground = grayValue < 15;

        if (isGreenScreen || isBlueScreen || isWhiteBackground || isBlackBackground) {
            backgroundPixels++;
        }
    }

    const backgroundPercentage = (backgroundPixels / totalPixels) * 100;
    console.log(`Frame analysis: ${backgroundPercentage.toFixed(1)}% detectado como fondo`);

    if (backgroundPercentage > 90) {
        console.warn('¡Advertencia! Se está detectando demasiado como fondo. El resultado puede ser completamente transparente.');
    }

    switch (method) {
        case 'ai':
            if (aiRemover) {
                return await aiRemover.processImage(imageData);
            }
            break;

        case 'webgpu':
            if (webgpuRemover) {
                return await webgpuRemover.processImage(imageData, {
                    threshold: options.threshold,
                    feather: options.feather
                });
            }
            break;

        case 'webgl':
            return await removeBackgroundWebGL(imageData);

        case 'cpu':
        default:
            return await processFrameCPUEdgeDetection(imageData, options);
    }

    // Fallback a CPU si el método preferido falla
    console.log('Usando fallback CPU con detección de bordes');
    return await processFrameCPUEdgeDetection(imageData, options);
};

// Función de prueba que no modifica la imagen (para depuración)
const processFrameNoChange = async (imageData: ImageData): Promise<ImageData> => {
    console.log('Procesando frame sin cambios (modo depuración)');

    // Crear una copia exacta sin modificaciones
    const result = new ImageData(imageData.width, imageData.height);
    result.data.set(imageData.data);

    return result;
};

// Función para visualizar qué se detecta como fondo (en rojo)
const processFrameVisualize = async (imageData: ImageData): Promise<ImageData> => {
    console.log('Modo visualización: mostrando detección de fondo en rojo');

    const { width, height } = imageData;
    const result = new ImageData(width, height);
    const data = imageData.data;
    const resultData = result.data;

    // Función de detección MUY conservadora para visualizar
    const isBackgroundPixel = (r: number, g: number, b: number): boolean => {
        // Solo detectar fondos EXTREMADAMENTE obvios

        // Chroma key muy estricto (verde brillante)
        const isGreenScreen = g > 180 && r < 50 && b < 50;

        // Chroma key muy estricto (azul brillante)
        const isBlueScreen = b > 180 && r < 50 && g < 50;

        // Blanco muy puro
        const isWhite = r > 240 && g > 240 && b > 240;

        // Negro muy puro
        const isBlack = r < 15 && g < 15 && b < 15;

        return isGreenScreen || isBlueScreen || isWhite || isBlack;
    };

    let backgroundCount = 0;
    let totalPixels = width * height;

    for (let i = 0; i < totalPixels; i++) {
        const pixelIndex = i * 4;
        const r = data[pixelIndex];
        const g = data[pixelIndex + 1];
        const b = data[pixelIndex + 2];
        const a = data[pixelIndex + 3];

        if (isBackgroundPixel(r, g, b)) {
            // Marcar como fondo en rojo brillante
            resultData[pixelIndex] = 255;     // R
            resultData[pixelIndex + 1] = 0;   // G
            resultData[pixelIndex + 2] = 0;   // B
            resultData[pixelIndex + 3] = 255; // A
            backgroundCount++;
        } else {
            // Mantener píxel original (sujeto)
            resultData[pixelIndex] = r;
            resultData[pixelIndex + 1] = g;
            resultData[pixelIndex + 2] = b;
            resultData[pixelIndex + 3] = a;
        }
    }

    const backgroundPercentage = (backgroundCount / totalPixels) * 100;
    console.log(`VISUALIZACIÓN: ${backgroundPercentage.toFixed(1)}% detectado como fondo (marcado en rojo)`);

    return result;
};

// Función para cargar imagen estática a ImageData
const loadImageToImageData = async (imageFile: File): Promise<ImageData> => {
    return new Promise((resolve, reject) => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        if (!ctx) {
            reject(new Error('No se pudo crear contexto de canvas'));
            return;
        }

        const img = new Image();
        img.onload = () => {
            canvas.width = img.width;
            canvas.height = img.height;

            ctx.drawImage(img, 0, 0);
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

            resolve(imageData);
        };

        img.onerror = (error) => {
            console.error('Error cargando imagen:', error);
            reject(new Error('Error cargando imagen'));
        };

        img.src = URL.createObjectURL(imageFile);
    });
};

// Función para convertir ImageData a Blob
const imageDataToBlob = async (imageData: ImageData): Promise<Blob> => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    if (!ctx) {
        throw new Error('No se pudo crear contexto de canvas');
    }

    canvas.width = imageData.width;
    canvas.height = imageData.height;
    ctx.putImageData(imageData, 0, 0);

    return new Promise((resolve, reject) => {
        canvas.toBlob((blob) => {
            if (blob) {
                resolve(blob);
            } else {
                reject(new Error('Error creando imagen'));
            }
        }, 'image/png');
    });
};

const processFrameCPU = async (
    imageData: ImageData,
    options: ProcessingOptions
): Promise<ImageData> => {
    const { width, height } = imageData;
    const result = new ImageData(width, height);
    const data = imageData.data;
    const resultData = result.data;

    // Función para detectar si un píxel es fondo (versión ULTRA conservadora)
    const isBackgroundPixel = (r: number, g: number, b: number): boolean => {
        // SOLO detectar fondos EXTREMADAMENTE obvios

        // Chroma key muy estricto (verde brillante)
        const isGreenScreen = g > 180 && r < 50 && b < 50;

        // Chroma key muy estricto (azul brillante)
        const isBlueScreen = b > 180 && r < 50 && g < 50;

        // Blanco muy puro (casi perfecto)
        const isWhite = r > 240 && g > 240 && b > 240;

        // Negro muy puro (casi perfecto)
        const isBlack = r < 15 && g < 15 && b < 15;

        // NO detectar grises, colores intermedios, o cualquier cosa que pueda ser parte del sujeto
        return isGreenScreen || isBlueScreen || isWhite || isBlack;
    };

    // Primera pasada: detectar píxeles de fondo
    const backgroundMask = new Array(width * height);
    for (let i = 0; i < width * height; i++) {
        const pixelIndex = i * 4;
        const r = data[pixelIndex];
        const g = data[pixelIndex + 1];
        const b = data[pixelIndex + 2];

        backgroundMask[i] = isBackgroundPixel(r, g, b);
    }

    // Segunda pasada: aplicar suavizado y generar resultado
    for (let i = 0; i < width * height; i++) {
        const pixelIndex = i * 4;
        const r = data[pixelIndex];
        const g = data[pixelIndex + 1];
        const b = data[pixelIndex + 2];
        const a = data[pixelIndex + 3];

        let alpha = a;

        if (backgroundMask[i]) {
            // Es fondo - hacer transparente
            alpha = 0;
        } else {
            // No es fondo - verificar si está cerca de bordes para suavizar
            const edgeStrength = calculateEdgeStrength(i, width, height, backgroundMask);
            if (edgeStrength > 0.3) {
                alpha = Math.round(a * (1 - edgeStrength * 0.7));
            }
        }

        resultData[pixelIndex] = r;
        resultData[pixelIndex + 1] = g;
        resultData[pixelIndex + 2] = b;
        resultData[pixelIndex + 3] = alpha;
    }

    return result;
};

// Nuevo algoritmo basado en detección de bordes y análisis de regiones
const processFrameCPUEdgeDetection = async (
    imageData: ImageData,
    options: ProcessingOptions
): Promise<ImageData> => {
    const { width, height } = imageData;
    const result = new ImageData(width, height);
    const data = imageData.data;
    const resultData = result.data;

    console.log('Usando algoritmo de detección de bordes...');

    // Paso 1: Detectar bordes usando gradiente
    const edges = detectEdges(data, width, height);

    // Paso 2: Analizar regiones conectadas
    const regions = analyzeConnectedRegions(data, width, height);

    // Paso 3: Determinar qué regiones son fondo basándose en:
    // - Posición (esquinas más probables de ser fondo)
    // - Uniformidad de color
    // - Tamaño de la región
    const backgroundRegions = identifyBackgroundRegions(regions, width, height);

    let backgroundPixels = 0;

    // Paso 4: Aplicar máscara
    for (let i = 0; i < width * height; i++) {
        const pixelIndex = i * 4;
        const r = data[pixelIndex];
        const g = data[pixelIndex + 1];
        const b = data[pixelIndex + 2];
        const a = data[pixelIndex + 3];

        const regionId = regions[i];
        const isBackground = backgroundRegions.has(regionId);

        if (isBackground) {
            backgroundPixels++;
        }

        // Copiar píxel con alpha modificado
        resultData[pixelIndex] = r;
        resultData[pixelIndex + 1] = g;
        resultData[pixelIndex + 2] = b;
        resultData[pixelIndex + 3] = isBackground ? 0 : a;
    }

    const backgroundPercentage = (backgroundPixels / (width * height)) * 100;
    console.log(`Detección de bordes: ${backgroundPercentage.toFixed(1)}% detectado como fondo`);

    return result;
};

// Función simple de detección de bordes
const detectEdges = (data: Uint8ClampedArray, width: number, height: number): number[] => {
    const edges = new Array(width * height).fill(0);

    for (let y = 1; y < height - 1; y++) {
        for (let x = 1; x < width - 1; x++) {
            const i = y * width + x;
            const pixelIndex = i * 4;

            // Calcular gradiente usando operador Sobel simplificado
            const gx =
                -data[((y-1) * width + (x-1)) * 4] + data[((y-1) * width + (x+1)) * 4] +
                -2 * data[(y * width + (x-1)) * 4] + 2 * data[(y * width + (x+1)) * 4] +
                -data[((y+1) * width + (x-1)) * 4] + data[((y+1) * width + (x+1)) * 4];

            const gy =
                -data[((y-1) * width + (x-1)) * 4] - 2 * data[((y-1) * width + x) * 4] - data[((y-1) * width + (x+1)) * 4] +
                data[((y+1) * width + (x-1)) * 4] + 2 * data[((y+1) * width + x) * 4] + data[((y+1) * width + (x+1)) * 4];

            edges[i] = Math.sqrt(gx * gx + gy * gy);
        }
    }

    return edges;
};

// Análisis de regiones conectadas (simplificado)
const analyzeConnectedRegions = (data: Uint8ClampedArray, width: number, height: number): number[] => {
    const regions = new Array(width * height).fill(-1);
    let currentRegion = 0;

    // Función para verificar si dos píxeles son similares
    const areSimilar = (i1: number, i2: number): boolean => {
        const r1 = data[i1 * 4], g1 = data[i1 * 4 + 1], b1 = data[i1 * 4 + 2];
        const r2 = data[i2 * 4], g2 = data[i2 * 4 + 1], b2 = data[i2 * 4 + 2];

        const diff = Math.abs(r1 - r2) + Math.abs(g1 - g2) + Math.abs(b1 - b2);
        return diff < 30; // Umbral de similitud
    };

    // Flood fill simplificado
    for (let i = 0; i < width * height; i++) {
        if (regions[i] === -1) {
            floodFill(i, currentRegion, regions, data, width, height, areSimilar);
            currentRegion++;
        }
    }

    return regions;
};

// Flood fill para regiones conectadas
const floodFill = (
    startIndex: number,
    regionId: number,
    regions: number[],
    data: Uint8ClampedArray,
    width: number,
    height: number,
    areSimilar: (i1: number, i2: number) => boolean
): void => {
    const stack = [startIndex];

    while (stack.length > 0) {
        const index = stack.pop()!;
        if (regions[index] !== -1) continue;

        regions[index] = regionId;

        const x = index % width;
        const y = Math.floor(index / width);

        // Verificar vecinos
        const neighbors = [
            { x: x - 1, y }, { x: x + 1, y },
            { x, y: y - 1 }, { x, y: y + 1 }
        ];

        for (const neighbor of neighbors) {
            if (neighbor.x >= 0 && neighbor.x < width && neighbor.y >= 0 && neighbor.y < height) {
                const neighborIndex = neighbor.y * width + neighbor.x;
                if (regions[neighborIndex] === -1 && areSimilar(index, neighborIndex)) {
                    stack.push(neighborIndex);
                }
            }
        }
    }
};

// Identificar qué regiones son probablemente fondo
const identifyBackgroundRegions = (regions: number[], width: number, height: number): Set<number> => {
    const regionStats = new Map<number, { count: number, borderPixels: number }>();

    // Contar píxeles y píxeles de borde para cada región
    for (let y = 0; y < height; y++) {
        for (let x = 0; x < width; x++) {
            const index = y * width + x;
            const regionId = regions[index];

            if (!regionStats.has(regionId)) {
                regionStats.set(regionId, { count: 0, borderPixels: 0 });
            }

            const stats = regionStats.get(regionId)!;
            stats.count++;

            // Verificar si está en el borde de la imagen
            if (x === 0 || x === width - 1 || y === 0 || y === height - 1) {
                stats.borderPixels++;
            }
        }
    }

    const backgroundRegions = new Set<number>();
    const totalPixels = width * height;

    // Una región es probablemente fondo si:
    // 1. Toca los bordes de la imagen
    // 2. Es muy grande (>30% de la imagen)
    // 3. Tiene alta proporción de píxeles de borde
    for (const [regionId, stats] of regionStats) {
        const borderRatio = stats.borderPixels / stats.count;
        const sizeRatio = stats.count / totalPixels;

        if (borderRatio > 0.1 || sizeRatio > 0.3) {
            backgroundRegions.add(regionId);
        }
    }

    console.log(`Identificadas ${backgroundRegions.size} regiones como fondo de ${regionStats.size} regiones totales`);

    return backgroundRegions;
};

const calculateEdgeStrength = (
    index: number,
    width: number,
    height: number,
    backgroundMask: boolean[]
): number => {
    const x = index % width;
    const y = Math.floor(index / width);

    let backgroundCount = 0;
    let totalSamples = 0;

    // Verificar píxeles vecinos en un radio de 2
    for (let dy = -2; dy <= 2; dy++) {
        for (let dx = -2; dx <= 2; dx++) {
            const nx = x + dx;
            const ny = y + dy;

            if (nx >= 0 && nx < width && ny >= 0 && ny < height) {
                const neighborIndex = ny * width + nx;
                if (backgroundMask[neighborIndex]) {
                    backgroundCount++;
                }
                totalSamples++;
            }
        }
    }

    return backgroundCount / totalSamples;
};

export const getProcessingInfo = (): ProcessingInfo | null => {
    if (!gpuCapabilities) {
        return null;
    }

    return {
        gpuInfo: formatGPUInfo(gpuCapabilities),
        method: getBestProcessingMethod(gpuCapabilities),
        framesCount: 0,
        processingTime: 0
    };
};

export const disposeProcessors = (): void => {
    if (webgpuRemover) {
        webgpuRemover.dispose();
        webgpuRemover = null;
    }

    if (aiRemover) {
        aiRemover.dispose();
        aiRemover = null;
    }
};
