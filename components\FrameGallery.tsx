import React, { useState, useEffect, useCallback } from 'react';

export interface Frame {
    id: string;
    originalImageData: ImageData;
    editedImageData?: ImageData;
    delay: number;
    filePath?: string;
    isEdited: boolean;
    lastModified?: number;
}

interface FrameGalleryProps {
    frames: Frame[];
    onFrameUpdate: (frameId: string, newImageData: ImageData) => void;
    onExportFrame: (frame: Frame) => void;
    onOpenFolder: () => void;
    isWatching: boolean;
}

export const FrameGallery: React.FC<FrameGalleryProps> = ({
    frames,
    onFrameUpdate,
    onExportFrame,
    onOpenFolder,
    isWatching
}) => {
    const [selectedFrames, setSelectedFrames] = useState<Set<string>>(new Set());
    const [contextMenu, setContextMenu] = useState<{
        x: number;
        y: number;
        frameId: string;
    } | null>(null);

    // Cerrar menú contextual al hacer click fuera
    useEffect(() => {
        const handleClick = () => setContextMenu(null);
        document.addEventListener('click', handleClick);
        return () => document.removeEventListener('click', handleClick);
    }, []);

    const handleFrameClick = useCallback((frameId: string, event: React.MouseEvent) => {
        if (event.ctrlKey || event.metaKey) {
            // Selección múltiple
            setSelectedFrames(prev => {
                const newSet = new Set(prev);
                if (newSet.has(frameId)) {
                    newSet.delete(frameId);
                } else {
                    newSet.add(frameId);
                }
                return newSet;
            });
        } else {
            // Selección simple
            setSelectedFrames(new Set([frameId]));
        }
    }, []);

    const handleContextMenu = useCallback((event: React.MouseEvent, frameId: string) => {
        event.preventDefault();
        setContextMenu({
            x: event.clientX,
            y: event.clientY,
            frameId
        });
    }, []);

    const handleEditFrame = useCallback((frameId: string) => {
        const frame = frames.find(f => f.id === frameId);
        if (frame) {
            onExportFrame(frame);
        }
        setContextMenu(null);
    }, [frames, onExportFrame]);

    const handleSelectAll = useCallback(() => {
        setSelectedFrames(new Set(frames.map(f => f.id)));
    }, [frames]);

    const handleDeselectAll = useCallback(() => {
        setSelectedFrames(new Set());
    }, []);

    const getFrameImageUrl = useCallback((frame: Frame): string => {
        const imageData = frame.editedImageData || frame.originalImageData;
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        if (!ctx) return '';
        
        canvas.width = imageData.width;
        canvas.height = imageData.height;
        ctx.putImageData(imageData, 0, 0);
        
        return canvas.toDataURL();
    }, []);

    if (frames.length === 0) {
        return (
            <div className="flex items-center justify-center h-64 bg-gray-800 rounded-xl border-2 border-dashed border-gray-600">
                <div className="text-center">
                    <div className="text-4xl mb-4">🎬</div>
                    <p className="text-gray-400">No hay frames para mostrar</p>
                    <p className="text-sm text-gray-500">Sube un GIF para comenzar</p>
                </div>
            </div>
        );
    }

    return (
        <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
            {/* Header con controles */}
            <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-4">
                    <h3 className="text-xl font-bold text-gray-200">
                        Galería de Frames ({frames.length})
                    </h3>
                    {isWatching && (
                        <div className="flex items-center space-x-2 text-green-400">
                            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                            <span className="text-sm">Monitoreando cambios</span>
                        </div>
                    )}
                </div>
                
                <div className="flex items-center space-x-3">
                    <span className="text-sm text-gray-400">
                        {selectedFrames.size} seleccionados
                    </span>

                    <div className="flex items-center space-x-2">
                        <button
                            onClick={handleSelectAll}
                            className="px-3 py-1 text-xs bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
                        >
                            Todos
                        </button>
                        <button
                            onClick={handleDeselectAll}
                            className="px-3 py-1 text-xs bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
                        >
                            Ninguno
                        </button>
                    </div>

                    <button
                        onClick={onOpenFolder}
                        className="px-4 py-2 bg-emerald-600 hover:bg-emerald-700 text-white rounded-lg font-medium transition-colors flex items-center space-x-2"
                        title="Abrir carpeta donde están los archivos exportados"
                    >
                        <span>📁</span>
                        <span>Abrir Carpeta</span>
                    </button>
                </div>
            </div>

            {/* Grid de frames */}
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
                {frames.map((frame, index) => (
                    <div
                        key={frame.id}
                        className={`relative group cursor-pointer rounded-lg overflow-hidden border-2 transition-all duration-200 ${
                            selectedFrames.has(frame.id)
                                ? 'border-purple-500 ring-2 ring-purple-500/50'
                                : 'border-gray-600 hover:border-gray-500'
                        }`}
                        onClick={(e) => handleFrameClick(frame.id, e)}
                        onContextMenu={(e) => handleContextMenu(e, frame.id)}
                    >
                        {/* Imagen del frame */}
                        <div className="aspect-square bg-gray-900 flex items-center justify-center">
                            <img
                                src={getFrameImageUrl(frame)}
                                alt={`Frame ${index + 1}`}
                                className="max-w-full max-h-full object-contain"
                                draggable={false}
                            />
                        </div>

                        {/* Overlay con información */}
                        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/50 transition-colors duration-200">
                            <div className="absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                <span className="bg-black/75 text-white text-xs px-2 py-1 rounded">
                                    #{index + 1}
                                </span>
                            </div>
                            
                            <div className="absolute bottom-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                <span className="bg-black/75 text-white text-xs px-2 py-1 rounded">
                                    {frame.delay}ms
                                </span>
                            </div>

                            {frame.isEdited && (
                                <div className="absolute top-2 right-2">
                                    <div className="w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
                                </div>
                            )}
                        </div>

                        {/* Botón de edición rápida */}
                        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                            <button
                                onClick={(e) => {
                                    e.stopPropagation();
                                    handleEditFrame(frame.id);
                                }}
                                className="bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded-full text-sm font-medium transition-colors"
                            >
                                Editar
                            </button>
                        </div>
                    </div>
                ))}
            </div>

            {/* Menú contextual */}
            {contextMenu && (
                <div
                    className="fixed bg-gray-800 border border-gray-600 rounded-lg shadow-xl py-2 z-50"
                    style={{
                        left: contextMenu.x,
                        top: contextMenu.y
                    }}
                >
                    <button
                        onClick={() => handleEditFrame(contextMenu.frameId)}
                        className="w-full px-4 py-2 text-left text-gray-200 hover:bg-gray-700 flex items-center space-x-2"
                    >
                        <span>✏️</span>
                        <span>Editar con programa externo</span>
                    </button>
                    <button
                        onClick={() => {
                            onOpenFolder();
                            setContextMenu(null);
                        }}
                        className="w-full px-4 py-2 text-left text-gray-200 hover:bg-gray-700 flex items-center space-x-2"
                    >
                        <span>📁</span>
                        <span>Abrir carpeta de archivos</span>
                    </button>
                    <button
                        onClick={() => {
                            // TODO: Implementar duplicar frame
                            setContextMenu(null);
                        }}
                        className="w-full px-4 py-2 text-left text-gray-200 hover:bg-gray-700 flex items-center space-x-2"
                    >
                        <span>📋</span>
                        <span>Duplicar frame</span>
                    </button>
                    <button
                        onClick={() => {
                            // TODO: Implementar eliminar frame
                            setContextMenu(null);
                        }}
                        className="w-full px-4 py-2 text-left text-red-400 hover:bg-gray-700 flex items-center space-x-2"
                    >
                        <span>🗑️</span>
                        <span>Eliminar frame</span>
                    </button>
                </div>
            )}

            {/* Información adicional */}
            <div className="mt-6 pt-4 border-t border-gray-700">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-400">
                    <div>
                        <span className="font-medium">Total frames:</span> {frames.length}
                    </div>
                    <div>
                        <span className="font-medium">Editados:</span> {frames.filter(f => f.isEdited).length}
                    </div>
                    <div>
                        <span className="font-medium">Seleccionados:</span> {selectedFrames.size}
                    </div>
                    <div>
                        <span className="font-medium">Duración aprox:</span>{' '}
                        {((frames.reduce((sum, f) => sum + f.delay, 0)) / 1000).toFixed(1)}s
                    </div>
                </div>
            </div>
        </div>
    );
};
