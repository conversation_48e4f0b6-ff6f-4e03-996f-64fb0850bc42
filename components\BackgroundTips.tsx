import React, { useState } from 'react';

export const BackgroundTips: React.FC = () => {
    const [isExpanded, setIsExpanded] = useState(false);

    return (
        <div className="bg-blue-900/30 border border-blue-700 rounded-xl p-4 mb-6">
            <div 
                className="flex items-center justify-between cursor-pointer"
                onClick={() => setIsExpanded(!isExpanded)}
            >
                <div className="flex items-center space-x-3">
                    <span className="text-2xl">💡</span>
                    <div>
                        <h3 className="text-lg font-semibold text-blue-200">
                            Consejos para Mejores Resultados
                        </h3>
                        <p className="text-sm text-blue-300">
                            Aprende qué tipos de fondos funcionan mejor
                        </p>
                    </div>
                </div>
                <svg 
                    className={`w-5 h-5 text-blue-400 transition-transform ${isExpanded ? 'rotate-180' : ''}`}
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
            </div>

            {isExpanded && (
                <div className="mt-4 space-y-4 border-t border-blue-700 pt-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* Fondos que funcionan bien */}
                        <div className="bg-green-900/30 border border-green-700 rounded-lg p-3">
                            <h4 className="font-semibold text-green-200 mb-2 flex items-center">
                                <span className="mr-2">✅</span>
                                Fondos que Funcionan Bien
                            </h4>
                            <ul className="text-sm text-green-300 space-y-1">
                                <li>• <strong>Fondos verdes/azules</strong> (chroma key)</li>
                                <li>• <strong>Fondos blancos</strong> o grises claros</li>
                                <li>• <strong>Fondos negros</strong> sólidos</li>
                                <li>• <strong>Colores saturados</strong> uniformes</li>
                                <li>• <strong>Fondos lisos</strong> sin texturas</li>
                            </ul>
                        </div>

                        {/* Fondos problemáticos */}
                        <div className="bg-yellow-900/30 border border-yellow-700 rounded-lg p-3">
                            <h4 className="font-semibold text-yellow-200 mb-2 flex items-center">
                                <span className="mr-2">⚠️</span>
                                Fondos Problemáticos
                            </h4>
                            <ul className="text-sm text-yellow-300 space-y-1">
                                <li>• <strong>Fondos complejos</strong> con muchos detalles</li>
                                <li>• <strong>Colores similares</strong> al sujeto principal</li>
                                <li>• <strong>Gradientes</strong> o transiciones</li>
                                <li>• <strong>Texturas</strong> o patrones</li>
                                <li>• <strong>Iluminación irregular</strong></li>
                            </ul>
                        </div>
                    </div>

                    {/* Configuraciones recomendadas */}
                    <div className="bg-purple-900/30 border border-purple-700 rounded-lg p-3">
                        <h4 className="font-semibold text-purple-200 mb-2 flex items-center">
                            <span className="mr-2">⚙️</span>
                            Configuraciones Recomendadas
                        </h4>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-3 text-sm">
                            <div className="text-purple-300">
                                <strong>Para Chroma Key:</strong>
                                <br />• Método: WebGPU/WebGL
                                <br />• Umbral: 40-60%
                                <br />• Suavizado: 5-15%
                            </div>
                            <div className="text-purple-300">
                                <strong>Para Retratos:</strong>
                                <br />• Método: IA
                                <br />• Modelo: Selfie/Retrato
                                <br />• Calidad: Alta
                            </div>
                            <div className="text-purple-300">
                                <strong>Para Fondos Complejos:</strong>
                                <br />• Método: IA
                                <br />• Modelo: General
                                <br />• Suavizado: 15-25%
                            </div>
                        </div>
                    </div>

                    {/* Ejemplos visuales */}
                    <div className="bg-gray-800 rounded-lg p-3">
                        <h4 className="font-semibold text-gray-200 mb-2 flex items-center">
                            <span className="mr-2">🎨</span>
                            Ejemplos de Resultados
                        </h4>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs text-gray-400">
                            <div className="text-center">
                                <div className="w-full h-12 bg-green-500 rounded mb-1"></div>
                                <span>Fondo Verde<br />Excelente</span>
                            </div>
                            <div className="text-center">
                                <div className="w-full h-12 bg-white rounded mb-1"></div>
                                <span>Fondo Blanco<br />Muy Bueno</span>
                            </div>
                            <div className="text-center">
                                <div className="w-full h-12 bg-blue-500 rounded mb-1"></div>
                                <span>Fondo Azul<br />Excelente</span>
                            </div>
                            <div className="text-center">
                                <div className="w-full h-12 bg-gradient-to-r from-red-500 to-yellow-500 rounded mb-1"></div>
                                <span>Gradiente<br />Difícil</span>
                            </div>
                        </div>
                    </div>

                    {/* Proceso paso a paso */}
                    <div className="bg-indigo-900/30 border border-indigo-700 rounded-lg p-3">
                        <h4 className="font-semibold text-indigo-200 mb-2 flex items-center">
                            <span className="mr-2">📋</span>
                            Proceso de Remoción
                        </h4>
                        <div className="flex flex-wrap gap-2 text-xs">
                            <div className="bg-indigo-800 px-2 py-1 rounded">1. Detectar GPU</div>
                            <div className="bg-indigo-800 px-2 py-1 rounded">2. Extraer Frames</div>
                            <div className="bg-indigo-800 px-2 py-1 rounded">3. Analizar Colores</div>
                            <div className="bg-indigo-800 px-2 py-1 rounded">4. Aplicar Máscara</div>
                            <div className="bg-indigo-800 px-2 py-1 rounded">5. Suavizar Bordes</div>
                            <div className="bg-indigo-800 px-2 py-1 rounded">6. Reconstruir GIF</div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};
