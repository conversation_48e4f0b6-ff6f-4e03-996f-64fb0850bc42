
import { GoogleGenAI } from "@google/genai";

// Ensure the API key is available from environment variables.
const API_KEY = process.env.API_KEY;
if (!API_KEY) {
  // In a real app, you might want to show a message to the user or handle this more gracefully.
  // For this context, throwing an error is sufficient.
  throw new Error("API_KEY environment variable not set. Please configure it to use the AI features.");
}
const ai = new GoogleGenAI({ apiKey: API_KEY });

/**
 * Analyzes a GIF image using the Gemini API.
 * @param base64Image The Base64 encoded string of the GIF image.
 * @returns A promise that resolves to a string containing the image description.
 */
export const analyzeImage = async (base64Image: string): Promise<string> => {
  try {
    const imagePart = {
      inlineData: {
        mimeType: 'image/gif',
        data: base64Image,
      },
    };
    const textPart = {
      text: "Describe en una sola frase y de forma concisa el sujeto principal de esta imagen. Sé creativo y descriptivo."
    };
    
    const response = await ai.models.generateContent({
      model: 'gemini-2.5-flash',
      contents: { parts: [imagePart, textPart] },
    });

    return response.text;
  } catch (error) {
    console.error("Error analyzing image with Gemini:", error);
    // Provide a more user-friendly error message
    throw new Error("No se pudo analizar la imagen. Es posible que el contenido no sea seguro o que se haya producido un error en la red. Inténtalo de nuevo.");
  }
};
