
import React, { useCallback, useState } from 'react';
import { UploadIcon } from './Icons';

type ImageUploaderProps = {
    onFileSelect: (file: File) => void;
};

export const ImageUploader: React.FC<ImageUploaderProps> = ({ onFileSelect }) => {
    const [isDragging, setIsDragging] = useState(false);

    const handleFile = (file: File | undefined | null) => {
        if (file && (file.type === 'image/gif' || file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/jpg')) {
            onFileSelect(file);
        } else {
            alert('Por favor, sube archivos GIF, JPG o PNG.');
        }
    };

    const handleDragEnter = useCallback((e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(true);
    }, []);

    const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);
    }, []);

    const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
    }, []);

    const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);
        const file = e.dataTransfer.files?.[0];
        handleFile(file);
    }, [onFileSelect]);

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        handleFile(file);
    };

    return (
        <div 
            className={`w-full max-w-2xl mx-auto flex flex-col justify-center items-center p-8 border-2 border-dashed rounded-2xl transition-all duration-300 ${isDragging ? 'border-cyan-400 bg-gray-800/50' : 'border-gray-600 hover:border-cyan-500 hover:bg-gray-800/30'}`}
            onDragEnter={handleDragEnter}
            onDragLeave={handleDragLeave}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
        >
            <UploadIcon className="w-16 h-16 text-gray-500 mb-4"/>
            <p className="text-xl text-gray-400 mb-2">Arrastra y suelta tu imagen aquí</p>
            <p className="text-sm text-gray-500 mb-2">Soporta: GIF, JPG, PNG</p>
            <p className="text-gray-500 mb-4">o</p>
            <label htmlFor="file-upload" className="cursor-pointer px-6 py-2 bg-gray-700 text-gray-200 font-semibold rounded-full hover:bg-gray-600 transition-colors">
                Seleccionar Archivo
            </label>
            <input
                id="file-upload"
                type="file"
                className="hidden"
                accept="image/gif,image/jpeg,image/jpg,image/png"
                onChange={handleFileChange}
            />
        </div>
    );
};
