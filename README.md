# Removedor de Fondo para GIF con GPU 🚀

Una aplicación avanzada para quitar el fondo de imágenes GIF usando aceleración por GPU (AMD y NVIDIA) e Inteligencia Artificial. Procesa cada frame del GIF individualmente y reconstruye un GIF animado con fondo transparente.

## ✨ Características Principales

### 🎯 Procesamiento Avanzado
- **Extracción completa de frames**: Separa todos los frames del GIF con sus delays originales
- **Múltiples métodos de procesamiento**: IA, WebGPU, WebGL y CPU
- **Reconstrucción real de GIF**: Genera GIFs animados reales, no imágenes estáticas
- **Detección automática de GPU**: Identifica y optimiza para tarjetas AMD, NVIDIA e Intel

### 🧠 Inteligencia Artificial
- **Modelos pre-entrenados**: Selfie, General y Retrato profesional
- **Algoritmos de segmentación**: Detección inteligente de sujetos vs fondo
- **Suavizado de bordes**: Transiciones naturales y anti-aliasing
- **Fallback inteligente**: Sistema robusto con múltiples niveles de respaldo

### ⚡ Aceleración por GPU
- **WebGPU**: Máximo rendimiento en GPUs modernas (AMD/NVIDIA)
- **Compute Shaders**: Procesamiento paralelo masivo
- **WebGL**: Compatibilidad amplia con tarjetas gráficas
- **Optimización automática**: Selecciona el mejor método según el hardware

### 🎨 Interfaz Moderna
- **Configuración avanzada**: Control total sobre parámetros de procesamiento
- **Información en tiempo real**: Estadísticas de GPU y rendimiento
- **Progreso detallado**: Seguimiento frame por frame
- **Diseño responsivo**: Optimizado para desktop y móvil

## 🛠️ Tecnologías

### Frontend
- **React 19** - Framework de UI moderno
- **TypeScript** - Tipado estático y mejor DX
- **Vite** - Build tool ultra-rápido
- **Tailwind CSS** - Estilos utilitarios

### Procesamiento
- **TensorFlow.js** - Machine Learning en el navegador
- **WebGPU/WebGL** - Aceleración por GPU
- **gif.js** - Manipulación de GIFs
- **gifuct-js** - Parser avanzado de GIFs

### IA y GPU
- **MediaPipe** - Modelos de segmentación
- **Compute Shaders** - Procesamiento paralelo
- **Backend Detection** - Optimización automática

## 🚀 Instalación y Uso

### Requisitos
- Node.js 18+
- Navegador moderno con soporte WebGL/WebGPU
- GPU recomendada (AMD/NVIDIA) para mejor rendimiento

### Instalación
```bash
# Instalar dependencias
npm install

# Iniciar servidor de desarrollo
npm run dev
```

### Uso Básico
1. **Abrir aplicación**: Navega a `http://localhost:5173`
2. **Configurar GPU**: Ajusta método de procesamiento y calidad
3. **Subir GIF**: Arrastra o selecciona tu archivo GIF
4. **Procesar**: Haz clic en "Procesar GIF" y espera
5. **Descargar**: Obtén tu GIF con fondo transparente

### Configuración Avanzada
- **Método**: Auto, IA, WebGPU, WebGL o CPU
- **Modelo de IA**: General, Selfie o Retrato
- **Calidad**: Alta, Media o Baja
- **Suavizado**: Control de bordes (0-50%)
- **Umbral**: Sensibilidad de detección (10-90%)
