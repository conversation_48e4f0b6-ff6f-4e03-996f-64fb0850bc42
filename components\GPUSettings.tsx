import React, { useState, useEffect } from 'react';
import { ProcessingOptions, getProcessingInfo } from '../services/backgroundRemovalService';

interface GPUSettingsProps {
    options: ProcessingOptions;
    onOptionsChange: (options: ProcessingOptions) => void;
    isProcessing: boolean;
}

export const GPUSettings: React.FC<GPUSettingsProps> = ({ 
    options, 
    onOptionsChange, 
    isProcessing 
}) => {
    const [gpuInfo, setGpuInfo] = useState<string>('Detectando GPU...');
    const [isExpanded, setIsExpanded] = useState(false);

    useEffect(() => {
        const loadGPUInfo = async () => {
            try {
                const info = getProcessingInfo();
                if (info) {
                    setGpuInfo(info.gpuInfo);
                } else {
                    setGpuInfo('GPU no detectada - usando CPU');
                }
            } catch (error) {
                setGpuInfo('Error detectando GPU');
            }
        };

        loadGPUInfo();
    }, []);

    const handleMethodChange = (method: ProcessingOptions['method']) => {
        onOptionsChange({ ...options, method });
    };

    const handleQualityChange = (quality: ProcessingOptions['quality']) => {
        onOptionsChange({ ...options, quality });
    };

    const handleAIModelChange = (aiModel: ProcessingOptions['aiModel']) => {
        onOptionsChange({ ...options, aiModel });
    };

    const handleFeatherChange = (feather: number) => {
        onOptionsChange({ ...options, feather });
    };

    const handleThresholdChange = (threshold: number) => {
        onOptionsChange({ ...options, threshold });
    };

    return (
        <div className="bg-gray-800 rounded-xl p-4 border border-gray-700 mb-6">
            <div 
                className="flex items-center justify-between cursor-pointer"
                onClick={() => setIsExpanded(!isExpanded)}
            >
                <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                    <div>
                        <h3 className="text-lg font-semibold text-gray-200">
                            Configuración de GPU
                        </h3>
                        <p className="text-sm text-gray-400">{gpuInfo}</p>
                    </div>
                </div>
                <svg 
                    className={`w-5 h-5 text-gray-400 transition-transform ${isExpanded ? 'rotate-180' : ''}`}
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
            </div>

            {isExpanded && (
                <div className="mt-4 space-y-4 border-t border-gray-700 pt-4">
                    {/* Método de procesamiento */}
                    <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                            Método de Procesamiento
                        </label>
                        <select
                            value={options.method}
                            onChange={(e) => handleMethodChange(e.target.value as ProcessingOptions['method'])}
                            disabled={isProcessing}
                            className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-gray-200 focus:outline-none focus:ring-2 focus:ring-purple-500 disabled:opacity-50"
                        >
                            <option value="auto">Automático (Recomendado)</option>
                            <option value="ai">Inteligencia Artificial</option>
                            <option value="webgpu">WebGPU (Máximo Rendimiento)</option>
                            <option value="webgl">WebGL (Compatible)</option>
                            <option value="cpu">CPU (Básico)</option>
                            <option value="debug">🔧 Debug (Sin Modificar)</option>
                        </select>
                    </div>

                    {/* Modelo de IA */}
                    {(options.method === 'ai' || options.method === 'auto') && (
                        <div>
                            <label className="block text-sm font-medium text-gray-300 mb-2">
                                Modelo de IA
                            </label>
                            <select
                                value={options.aiModel}
                                onChange={(e) => handleAIModelChange(e.target.value as ProcessingOptions['aiModel'])}
                                disabled={isProcessing}
                                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-gray-200 focus:outline-none focus:ring-2 focus:ring-purple-500 disabled:opacity-50"
                            >
                                <option value="general">General (Recomendado)</option>
                                <option value="selfie">Selfie/Retrato</option>
                                <option value="portrait">Retrato Profesional</option>
                            </select>
                        </div>
                    )}

                    {/* Calidad */}
                    <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                            Calidad de Procesamiento
                        </label>
                        <select
                            value={options.quality}
                            onChange={(e) => handleQualityChange(e.target.value as ProcessingOptions['quality'])}
                            disabled={isProcessing}
                            className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-gray-200 focus:outline-none focus:ring-2 focus:ring-purple-500 disabled:opacity-50"
                        >
                            <option value="high">Alta (Más lento)</option>
                            <option value="medium">Media (Recomendado)</option>
                            <option value="low">Baja (Más rápido)</option>
                        </select>
                    </div>

                    {/* Configuraciones avanzadas */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-300 mb-2">
                                Suavizado de Bordes: {(options.feather * 100).toFixed(0)}%
                            </label>
                            <input
                                type="range"
                                min="0"
                                max="0.5"
                                step="0.01"
                                value={options.feather}
                                onChange={(e) => handleFeatherChange(parseFloat(e.target.value))}
                                disabled={isProcessing}
                                className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
                            />
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-300 mb-2">
                                Umbral de Detección: {(options.threshold * 100).toFixed(0)}%
                            </label>
                            <input
                                type="range"
                                min="0.1"
                                max="0.9"
                                step="0.01"
                                value={options.threshold}
                                onChange={(e) => handleThresholdChange(parseFloat(e.target.value))}
                                disabled={isProcessing}
                                className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
                            />
                        </div>
                    </div>

                    {/* Información adicional */}
                    <div className="bg-gray-900 rounded-lg p-3 text-xs text-gray-400">
                        <p><strong>Automático:</strong> Selecciona el mejor método según tu hardware</p>
                        <p><strong>IA:</strong> Usa modelos de machine learning para mejor precisión</p>
                        <p><strong>WebGPU:</strong> Máximo rendimiento en GPUs modernas (AMD/NVIDIA)</p>
                        <p><strong>WebGL:</strong> Compatible con la mayoría de tarjetas gráficas</p>
                    </div>
                </div>
            )}
        </div>
    );
};
