/**
 * Procesamiento de remoción de fondo usando WebGPU para máximo rendimiento
 */

// Compute shader para remoción de fondo usando IA/ML
const computeShaderSource = `
@group(0) @binding(0) var<storage, read> inputImage: array<u32>;
@group(0) @binding(1) var<storage, read_write> outputImage: array<u32>;
@group(0) @binding(2) var<uniform> params: Params;

struct Params {
    width: u32,
    height: u32,
    threshold: f32,
    feather: f32,
}

// Función para convertir RGBA packed a componentes
fn unpackRGBA(packed: u32) -> vec4<f32> {
    let r = f32((packed >> 0u) & 0xFFu) / 255.0;
    let g = f32((packed >> 8u) & 0xFFu) / 255.0;
    let b = f32((packed >> 16u) & 0xFFu) / 255.0;
    let a = f32((packed >> 24u) & 0xFFu) / 255.0;
    return vec4<f32>(r, g, b, a);
}

// Función para empacar componentes RGBA
fn packRGBA(color: vec4<f32>) -> u32 {
    let r = u32(clamp(color.r * 255.0, 0.0, 255.0));
    let g = u32(clamp(color.g * 255.0, 0.0, 255.0));
    let b = u32(clamp(color.b * 255.0, 0.0, 255.0));
    let a = u32(clamp(color.a * 255.0, 0.0, 255.0));
    return r | (g << 8u) | (b << 16u) | (a << 24u);
}

// Algoritmo mejorado de detección de fondo
fn isBackground(color: vec3<f32>) -> f32 {
    // Detección de chroma key mejorada
    let greenScreen = step(0.47, color.g) * step(color.r, 0.31) * step(color.b, 0.31);
    let blueScreen = step(0.47, color.b) * step(color.r, 0.31) * step(color.g, 0.31);

    // Detección de fondo blanco/gris claro
    let grayValue = (color.r + color.g + color.b) / 3.0;
    let isGray = 1.0 - step(0.12, abs(color.r - grayValue)) * step(0.12, abs(color.g - grayValue)) * step(0.12, abs(color.b - grayValue));
    let whiteBackground = isGray * step(0.75, grayValue);

    // Detección de fondo negro
    let blackBackground = step(grayValue, 0.1);

    // Detección de colores muy saturados (posibles fondos artificiales)
    let maxChannel = max(max(color.r, color.g), color.b);
    let minChannel = min(min(color.r, color.g), color.b);
    let saturation = select(0.0, (maxChannel - minChannel) / maxChannel, maxChannel > 0.0);
    let saturatedBackground = step(0.8, saturation) * step(0.39, grayValue);

    // Combinar todas las detecciones
    return max(max(max(max(greenScreen, blueScreen), whiteBackground), blackBackground), saturatedBackground);
}

// Función de suavizado de bordes
fn edgeSmoothing(index: u32, isBackgroundPixel: f32) -> f32 {
    let x = index % params.width;
    let y = index / params.width;
    
    var neighborSum = 0.0;
    var neighborCount = 0.0;
    
    // Verificar píxeles vecinos en un radio de 2
    for (var dy = -2i; dy <= 2i; dy++) {
        for (var dx = -2i; dx <= 2i; dx++) {
            let nx = i32(x) + dx;
            let ny = i32(y) + dy;
            
            if (nx >= 0 && nx < i32(params.width) && ny >= 0 && ny < i32(params.height)) {
                let neighborIndex = u32(ny) * params.width + u32(nx);
                let neighborColor = unpackRGBA(inputImage[neighborIndex]);
                neighborSum += isBackground(neighborColor.rgb);
                neighborCount += 1.0;
            }
        }
    }
    
    let neighborAvg = neighborSum / neighborCount;
    
    // Suavizar basado en vecinos
    return mix(isBackgroundPixel, neighborAvg, params.feather);
}

@compute @workgroup_size(8, 8)
fn main(@builtin(global_invocation_id) global_id: vec3<u32>) {
    let index = global_id.y * params.width + global_id.x;
    
    if (global_id.x >= params.width || global_id.y >= params.height) {
        return;
    }
    
    let inputColor = unpackRGBA(inputImage[index]);
    let backgroundMask = isBackground(inputColor.rgb);
    
    // Aplicar suavizado de bordes
    let smoothedMask = edgeSmoothing(index, backgroundMask);
    
    // Calcular alpha final con transición suave
    let alpha = (1.0 - smoothedMask) * inputColor.a;
    
    let outputColor = vec4<f32>(inputColor.rgb, alpha);
    outputImage[index] = packRGBA(outputColor);
}
`;

export interface WebGPUProcessingOptions {
    threshold: number;
    feather: number;
}

export class WebGPUBackgroundRemover {
    private device: GPUDevice | null = null;
    private pipeline: GPUComputePipeline | null = null;
    private bindGroupLayout: GPUBindGroupLayout | null = null;

    async initialize(): Promise<boolean> {
        try {
            if (!('gpu' in navigator)) {
                throw new Error('WebGPU no está disponible');
            }

            const adapter = await navigator.gpu.requestAdapter();
            if (!adapter) {
                throw new Error('No se pudo obtener adaptador WebGPU');
            }

            this.device = await adapter.requestDevice();
            
            // Crear layout del bind group
            this.bindGroupLayout = this.device.createBindGroupLayout({
                entries: [
                    {
                        binding: 0,
                        visibility: GPUShaderStage.COMPUTE,
                        buffer: { type: 'read-only-storage' }
                    },
                    {
                        binding: 1,
                        visibility: GPUShaderStage.COMPUTE,
                        buffer: { type: 'storage' }
                    },
                    {
                        binding: 2,
                        visibility: GPUShaderStage.COMPUTE,
                        buffer: { type: 'uniform' }
                    }
                ]
            });

            // Crear pipeline de compute
            this.pipeline = this.device.createComputePipeline({
                layout: this.device.createPipelineLayout({
                    bindGroupLayouts: [this.bindGroupLayout]
                }),
                compute: {
                    module: this.device.createShaderModule({
                        code: computeShaderSource
                    }),
                    entryPoint: 'main'
                }
            });

            return true;

        } catch (error) {
            console.error('Error inicializando WebGPU:', error);
            return false;
        }
    }

    async processImage(
        imageData: ImageData, 
        options: WebGPUProcessingOptions = { threshold: 0.5, feather: 0.1 }
    ): Promise<ImageData> {
        if (!this.device || !this.pipeline || !this.bindGroupLayout) {
            throw new Error('WebGPU no está inicializado');
        }

        const { width, height } = imageData;
        const pixelCount = width * height;
        
        // Convertir ImageData a array de uint32
        const inputArray = new Uint32Array(pixelCount);
        const data = imageData.data;
        
        for (let i = 0; i < pixelCount; i++) {
            const offset = i * 4;
            const r = data[offset];
            const g = data[offset + 1];
            const b = data[offset + 2];
            const a = data[offset + 3];
            inputArray[i] = r | (g << 8) | (b << 16) | (a << 24);
        }

        // Crear buffers
        const inputBuffer = this.device.createBuffer({
            size: inputArray.byteLength,
            usage: GPUBufferUsage.STORAGE | GPUBufferUsage.COPY_DST
        });

        const outputBuffer = this.device.createBuffer({
            size: inputArray.byteLength,
            usage: GPUBufferUsage.STORAGE | GPUBufferUsage.COPY_SRC
        });

        const paramsArray = new Float32Array([width, height, options.threshold, options.feather]);
        const paramsBuffer = this.device.createBuffer({
            size: paramsArray.byteLength,
            usage: GPUBufferUsage.UNIFORM | GPUBufferUsage.COPY_DST
        });

        const readBuffer = this.device.createBuffer({
            size: inputArray.byteLength,
            usage: GPUBufferUsage.COPY_DST | GPUBufferUsage.MAP_READ
        });

        // Escribir datos a los buffers
        this.device.queue.writeBuffer(inputBuffer, 0, inputArray);
        this.device.queue.writeBuffer(paramsBuffer, 0, paramsArray);

        // Crear bind group
        const bindGroup = this.device.createBindGroup({
            layout: this.bindGroupLayout,
            entries: [
                { binding: 0, resource: { buffer: inputBuffer } },
                { binding: 1, resource: { buffer: outputBuffer } },
                { binding: 2, resource: { buffer: paramsBuffer } }
            ]
        });

        // Ejecutar compute shader
        const commandEncoder = this.device.createCommandEncoder();
        const passEncoder = commandEncoder.beginComputePass();
        
        passEncoder.setPipeline(this.pipeline);
        passEncoder.setBindGroup(0, bindGroup);
        
        const workgroupsX = Math.ceil(width / 8);
        const workgroupsY = Math.ceil(height / 8);
        passEncoder.dispatchWorkgroups(workgroupsX, workgroupsY);
        
        passEncoder.end();

        // Copiar resultado
        commandEncoder.copyBufferToBuffer(outputBuffer, 0, readBuffer, 0, inputArray.byteLength);
        
        this.device.queue.submit([commandEncoder.finish()]);

        // Leer resultado
        await readBuffer.mapAsync(GPUMapMode.READ);
        const resultArray = new Uint32Array(readBuffer.getMappedRange());
        
        // Convertir de vuelta a ImageData
        const resultImageData = new ImageData(width, height);
        const resultData = resultImageData.data;
        
        for (let i = 0; i < pixelCount; i++) {
            const pixel = resultArray[i];
            const offset = i * 4;
            resultData[offset] = pixel & 0xFF;
            resultData[offset + 1] = (pixel >> 8) & 0xFF;
            resultData[offset + 2] = (pixel >> 16) & 0xFF;
            resultData[offset + 3] = (pixel >> 24) & 0xFF;
        }

        readBuffer.unmap();

        // Limpiar buffers
        inputBuffer.destroy();
        outputBuffer.destroy();
        paramsBuffer.destroy();
        readBuffer.destroy();

        return resultImageData;
    }

    dispose(): void {
        this.device = null;
        this.pipeline = null;
        this.bindGroupLayout = null;
    }
}
