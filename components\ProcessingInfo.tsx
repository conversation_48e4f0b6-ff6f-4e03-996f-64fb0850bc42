import React from 'react';
import { ProcessingInfo as ProcessingInfoType } from '../services/backgroundRemovalService';

interface ProcessingInfoProps {
    info: ProcessingInfoType | null;
    isVisible: boolean;
}

export const ProcessingInfo: React.FC<ProcessingInfoProps> = ({ info, isVisible }) => {
    if (!isVisible || !info) {
        return null;
    }

    const formatTime = (ms: number): string => {
        if (ms < 1000) {
            return `${ms}ms`;
        }
        return `${(ms / 1000).toFixed(1)}s`;
    };

    const getMethodIcon = (method: string): string => {
        switch (method) {
            case 'webgpu':
                return '🚀';
            case 'ai':
                return '🧠';
            case 'webgl':
                return '⚡';
            case 'cpu':
                return '💻';
            default:
                return '🔧';
        }
    };

    const getMethodName = (method: string): string => {
        switch (method) {
            case 'webgpu':
                return 'WebGPU';
            case 'ai':
                return 'Inteligencia Artificial';
            case 'webgl':
                return 'WebGL';
            case 'cpu':
                return 'CPU';
            default:
                return 'Automático';
        }
    };

    const getPerformanceColor = (time: number, frames: number): string => {
        const timePerFrame = time / frames;
        if (timePerFrame < 100) return 'text-green-400';
        if (timePerFrame < 500) return 'text-yellow-400';
        return 'text-red-400';
    };

    return (
        <div className="bg-gray-800 rounded-xl p-4 border border-gray-700 mb-6 animate-fade-in-up">
            <h3 className="text-lg font-semibold text-gray-200 mb-3 flex items-center">
                <span className="mr-2">📊</span>
                Información de Procesamiento
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* GPU Info */}
                <div className="bg-gray-900 rounded-lg p-3">
                    <div className="text-xs text-gray-400 uppercase tracking-wide mb-1">
                        Hardware
                    </div>
                    <div className="text-sm text-gray-200 font-medium">
                        {info.gpuInfo}
                    </div>
                </div>

                {/* Método */}
                <div className="bg-gray-900 rounded-lg p-3">
                    <div className="text-xs text-gray-400 uppercase tracking-wide mb-1">
                        Método
                    </div>
                    <div className="text-sm text-gray-200 font-medium flex items-center">
                        <span className="mr-2">{getMethodIcon(info.method)}</span>
                        {getMethodName(info.method)}
                    </div>
                </div>

                {/* Frames */}
                <div className="bg-gray-900 rounded-lg p-3">
                    <div className="text-xs text-gray-400 uppercase tracking-wide mb-1">
                        Frames Procesados
                    </div>
                    <div className="text-sm text-gray-200 font-medium">
                        {info.framesCount} frames
                    </div>
                </div>

                {/* Tiempo */}
                <div className="bg-gray-900 rounded-lg p-3">
                    <div className="text-xs text-gray-400 uppercase tracking-wide mb-1">
                        Tiempo Total
                    </div>
                    <div className={`text-sm font-medium ${getPerformanceColor(info.processingTime, info.framesCount)}`}>
                        {formatTime(info.processingTime)}
                    </div>
                </div>
            </div>

            {/* Estadísticas adicionales */}
            <div className="mt-4 pt-4 border-t border-gray-700">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div className="text-gray-400">
                        <span className="font-medium">Tiempo por frame:</span>{' '}
                        <span className={getPerformanceColor(info.processingTime, info.framesCount)}>
                            {formatTime(info.processingTime / info.framesCount)}
                        </span>
                    </div>
                    
                    <div className="text-gray-400">
                        <span className="font-medium">FPS de procesamiento:</span>{' '}
                        <span className="text-gray-200">
                            {(info.framesCount / (info.processingTime / 1000)).toFixed(1)} fps
                        </span>
                    </div>
                    
                    <div className="text-gray-400">
                        <span className="font-medium">Eficiencia:</span>{' '}
                        <span className={`font-medium ${
                            info.processingTime < 5000 ? 'text-green-400' :
                            info.processingTime < 15000 ? 'text-yellow-400' : 'text-red-400'
                        }`}>
                            {info.processingTime < 5000 ? 'Excelente' :
                             info.processingTime < 15000 ? 'Buena' : 'Mejorable'}
                        </span>
                    </div>
                </div>
            </div>

            {/* Recomendaciones */}
            {info.processingTime > 10000 && (
                <div className="mt-4 p-3 bg-yellow-900/30 border border-yellow-700 rounded-lg">
                    <div className="flex items-start">
                        <span className="text-yellow-400 mr-2">💡</span>
                        <div className="text-sm text-yellow-200">
                            <strong>Sugerencia:</strong> Para mejorar el rendimiento, prueba:
                            <ul className="mt-1 ml-4 list-disc text-xs text-yellow-300">
                                <li>Reducir la calidad a "Media" o "Baja"</li>
                                <li>Usar método "WebGL" en lugar de "IA"</li>
                                <li>Procesar GIFs con menos frames</li>
                            </ul>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};
